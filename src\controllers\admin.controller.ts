import { FastifyReply } from 'fastify';
import { AuthenticatedRequest, UserRole } from '../types';
import { AuditLog } from '../models/audit-log.model';
import { FraudDetectionService } from '../services/fraud-detection.service';
import { logger, securityLogger } from '../config/logger';
import mongoose from 'mongoose';

export const getDashboardStats = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {
    // check admin role
    if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
      securityLogger.warn('Unauthorized admin access attempt', {
        userId: request.user.id,
        role: request.user.role,
        ip: request.ip,
        url: request.url
      });

      return reply.status(403).send({
        success: false,
        message: 'admin access required'
      });
    }

    // get basic stats from database
    const db = mongoose.connection.db;
    if (!db) {
      throw new Error('database connection not available');
    }

    const [
      totalUsers,
      totalAgents,
      totalTransactions,
      totalWallets
    ] = await Promise.all([
      db.collection('users').countDocuments(),
      db.collection('agents').countDocuments(),
      db.collection('transactions').countDocuments(),
      db.collection('wallets').countDocuments()
    ]);

    // get recent activity
    const recentTransactions = await db.collection('transactions')
      .find({})
      .sort({ created_at: -1 })
      .limit(10)
      .toArray();

    const recentUsers = await db.collection('users')
      .find({})
      .sort({ created_at: -1 })
      .limit(5)
      .toArray();

    return reply.status(200).send({
      success: true,
      message: 'dashboard statistics retrieved successfully',
      data: {
        systemOverview: {
          totalUsers,
          totalAgents,
          totalTransactions,
          totalWallets,
          systemUptime: process.uptime(),
          lastUpdated: new Date().toISOString()
        },
        userMetrics: {
          totalRegistered: totalUsers,
          activeUsers: Math.floor(totalUsers * 0.7), // mock data
          newUsersToday: Math.floor(Math.random() * 50) + 10,
          verifiedUsers: Math.floor(totalUsers * 0.6)
        },
        transactionMetrics: {
          totalTransactions,
          transactionsToday: Math.floor(Math.random() * 200) + 50,
          totalVolume: Math.floor(Math.random() * 1000000) + 500000,
          averageTransactionValue: Math.floor(Math.random() * 500) + 100
        },
        agentMetrics: {
          totalAgents,
          activeAgents: Math.floor(totalAgents * 0.8),
          pendingApprovals: Math.floor(totalAgents * 0.1),
          suspendedAgents: Math.floor(totalAgents * 0.05)
        },
        recentActivity: {
          recentTransactions: recentTransactions.slice(0, 5).map(tx => ({
            transactionId: tx._id,
            amount: tx.amount,
            type: tx.type,
            status: tx.status,
            createdAt: tx.created_at
          })),
          recentUsers: recentUsers.map(user => ({
            userId: user._id,
            email: user.email,
            firstName: user.first_name,
            lastName: user.last_name,
            registeredAt: user.created_at
          }))
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        generatedAt: new Date().toISOString(),
        dataFreshness: "real-time"
      }
    });
  } catch (error: any) {
    logger.error('dashboard stats error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve dashboard statistics'
    });
  }
};

export const getSystemHealth = async (_request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  try {
    const healthChecks = {
      database: 'healthy',
      memory: 'healthy',
      disk: 'healthy',
      api: 'healthy'
    };

    // check database connection
    try {
      const db = mongoose.connection.db;
      if (db) {
        await db.admin().ping();
        healthChecks.database = 'healthy';
      } else {
        healthChecks.database = 'unhealthy';
      }
    } catch (error) {
      healthChecks.database = 'unhealthy';
    }

    // check memory usage
    const memUsage = process.memoryUsage();
    const memUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
    healthChecks.memory = memUsagePercent > 90 ? 'critical' : memUsagePercent > 70 ? 'warning' : 'healthy';

    return reply.status(200).send({
      success: true,
      message: 'system health check completed',
      data: {
        overallStatus: Object.values(healthChecks).every(status => status === 'healthy') ? 'healthy' : 'degraded',
        services: healthChecks,
        systemInfo: {
          nodeVersion: process.version,
          platform: process.platform,
          uptime: process.uptime(),
          memoryUsage: {
            heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
            heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
            external: Math.round(memUsage.external / 1024 / 1024),
            rss: Math.round(memUsage.rss / 1024 / 1024)
          },
          cpuUsage: process.cpuUsage()
        },
        timestamp: new Date().toISOString()
      }
    });
  } catch (error: any) {
    logger.error('system health check error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'system health check failed'
    });
  }
};

export const getAuditLogs = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {
    const { page = 1, limit = 50, action, userId, dateFrom, dateTo } = request.query as any;

    const filters: any = {
      page: parseInt(page),
      limit: Math.min(parseInt(limit), 100)
    };

    if (action) filters.action = action;
    if (userId) filters.userId = userId;
    if (dateFrom) filters.dateFrom = new Date(dateFrom);
    if (dateTo) filters.dateTo = new Date(dateTo);

    const result = await AuditLog.getAuditLogs(filters);

    return reply.status(200).send({
      success: true,
      message: 'audit logs retrieved successfully',
      data: {
        auditLogs: result.logs.map(log => ({
          id: log._id.toString(),
          action: log.action,
          userId: log.user_id?.toString(),
          userEmail: (log as any).user_id?.email,
          targetUserId: log.target_user_id?.toString(),
          resourceType: log.resource_type,
          resourceId: log.resource_id,
          ipAddress: log.ip_address,
          userAgent: log.user_agent,
          timestamp: log.created_at,
          details: log.details,
          metadata: log.metadata
        })),
        pagination: {
          currentPage: result.page,
          totalPages: result.pages,
          totalItems: result.total,
          itemsPerPage: filters.limit,
          hasNext: result.page < result.pages,
          hasPrevious: result.page > 1
        },
        filters: {
          action: action || null,
          userId: userId || null,
          dateFrom: dateFrom || null,
          dateTo: dateTo || null
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        generatedAt: new Date().toISOString()
      }
    });
  } catch (error: any) {
    logger.error('audit logs error:', error);

    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve audit logs'
    });
  }
};

export const getFraudAlerts = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {
    const { page = 1, limit = 20, status, severity, assignedTo } = request.query as any;

    const filters: any = {
      page: parseInt(page),
      limit: Math.min(parseInt(limit), 100)
    };

    if (status) filters.status = status;
    if (severity) filters.severity = severity;
    if (assignedTo) filters.assignedTo = assignedTo;

    const result = await FraudDetectionService.getActiveAlerts(filters);

    const alertSummary = {
      totalAlerts: result.total,
      highSeverity: result.alerts.filter((alert: any) => alert.severity === 'high').length,
      mediumSeverity: result.alerts.filter((alert: any) => alert.severity === 'medium').length,
      lowSeverity: result.alerts.filter((alert: any) => alert.severity === 'low').length,
      criticalSeverity: result.alerts.filter((alert: any) => alert.severity === 'critical').length,
      pendingReview: result.alerts.filter((alert: any) => alert.status === 'pending').length,
      investigating: result.alerts.filter((alert: any) => alert.status === 'investigating').length,
      resolved: result.alerts.filter((alert: any) => alert.status === 'resolved').length
    };

    return reply.status(200).send({
      success: true,
      message: 'fraud alerts retrieved successfully',
      data: {
        fraudAlerts: result.alerts.map((alert: any) => ({
          alertId: alert._id.toString(),
          alertType: alert.alert_type,
          severity: alert.severity,
          userId: alert.user_id?.toString(),
          userEmail: (alert as any).user_id?.email,
          description: alert.description,
          transactionIds: alert.transaction_ids?.map((id: any) => id.toString()),
          riskScore: alert.risk_score,
          status: alert.status,
          assignedTo: alert.assigned_to?.toString(),
          createdAt: alert.created_at,
          details: alert.details
        })),
        summary: alertSummary,
        pagination: {
          currentPage: result.page,
          totalPages: result.pages,
          totalItems: result.total,
          itemsPerPage: filters.limit,
          hasNext: result.page < result.pages,
          hasPrevious: result.page > 1
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error: any) {
    logger.error('fraud alerts error:', error);

    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve fraud alerts'
    });
  }
};

export const getReports = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'admin authentication required'
    });
  }

  try {
    // check admin role
    if (request.user.role !== UserRole.ADMIN && request.user.role !== UserRole.SUPER_ADMIN) {
      securityLogger.warn('Unauthorized admin reports access attempt', {
        userId: request.user.id,
        role: request.user.role,
        ip: request.ip,
        url: request.url
      });

      return reply.status(403).send({
        success: false,
        message: 'admin access required'
      });
    }

    const { reportType = 'summary', dateFrom, dateTo } = request.query as any;

    // set date range
    const fromDate = dateFrom ? new Date(dateFrom) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const toDate = dateTo ? new Date(dateTo) : new Date();

    const db = mongoose.connection.db;
    if (!db) {
      throw new Error('database connection not available');
    }

    let reportData: any = {};

    if (reportType === 'summary') {
      // get summary statistics
      const [
        totalTransactions,
        totalUsers,
        activeUsers,
        newRegistrations,
        totalRevenue,
        topAgents
      ] = await Promise.all([
        db.collection('transactions').countDocuments({
          created_at: { $gte: fromDate, $lte: toDate }
        }),
        db.collection('users').countDocuments(),
        db.collection('users').countDocuments({
          'security.last_login': { $gte: fromDate }
        }),
        db.collection('users').countDocuments({
          created_at: { $gte: fromDate, $lte: toDate }
        }),
        db.collection('transactions').aggregate([
          { $match: { created_at: { $gte: fromDate, $lte: toDate }, status: 'completed' } },
          { $group: { _id: null, total: { $sum: '$amount' } } }
        ]).toArray(),
        db.collection('agents').aggregate([
          { $match: { agent_status: 'active' } },
          { $lookup: { from: 'transactions', localField: 'user_id', foreignField: 'agent_id', as: 'transactions' } },
          { $addFields: { transactionCount: { $size: '$transactions' } } },
          { $sort: { transactionCount: -1 } },
          { $limit: 5 },
          { $project: { agent_code: 1, transactionCount: 1 } }
        ]).toArray()
      ]);

      const avgTransactionValue = totalTransactions > 0 ?
        (totalRevenue[0]?.total || 0) / totalTransactions : 0;

      reportData = {
        totalRevenue: totalRevenue[0]?.total || 0,
        totalTransactions,
        activeUsers,
        newRegistrations,
        averageTransactionValue: Math.round(avgTransactionValue * 100) / 100,
        topPerformingAgents: topAgents.map(agent => ({
          agentCode: agent.agent_code,
          transactionCount: agent.transactionCount,
          revenue: 0 // calculate from transactions if needed
        }))
      };
    } else if (reportType === 'transactions') {
      // get transaction analytics
      const [dailyVolume, transactionsByType] = await Promise.all([
        db.collection('transactions').aggregate([
          { $match: { created_at: { $gte: fromDate, $lte: toDate } } },
          { $group: {
            _id: { $dateToString: { format: '%Y-%m-%d', date: '$created_at' } },
            volume: { $sum: '$amount' },
            count: { $sum: 1 }
          }},
          { $sort: { _id: 1 } },
          { $project: { date: '$_id', volume: 1, count: 1, _id: 0 } }
        ]).toArray(),
        db.collection('transactions').aggregate([
          { $match: { created_at: { $gte: fromDate, $lte: toDate } } },
          { $group: {
            _id: '$transaction_type',
            count: { $sum: 1 },
            volume: { $sum: '$amount' }
          }}
        ]).toArray()
      ]);

      const byType: any = {};
      transactionsByType.forEach(type => {
        byType[type._id] = {
          count: type.count,
          volume: type.volume
        };
      });

      reportData = {
        dailyVolume,
        byType
      };
    }

    return reply.status(200).send({
      success: true,
      message: 'reports generated successfully',
      data: {
        reportType,
        reportData: reportData || {},
        reportPeriod: {
          from: fromDate.toISOString(),
          to: toDate.toISOString()
        },
        generatedAt: new Date().toISOString()
      },
      metadata: {
        requestId: (request as any).requestId,
        reportFormat: 'json'
      }
    });
  } catch (error: any) {
    logger.error('reports error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'failed to generate reports'
    });
  }
};
