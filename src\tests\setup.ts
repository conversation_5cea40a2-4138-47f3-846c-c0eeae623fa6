import { config } from '../config';

// Set test environment
process.env.NODE_ENV = 'test';
process.env.MONGODB_URI = config.database.testUri;

// Increase test timeout for database operations
jest.setTimeout(30000);

// Global test setup
beforeAll(async () => {
  // Setup test database connection if needed
});

afterAll(async () => {
  // Cleanup test database if needed
});

// Mock external services for testing
jest.mock('../config/redis', () => ({
  redis: {
    connect: jest.fn(),
    disconnect: jest.fn(),
    set: jest.fn(),
    get: jest.fn(),
    del: jest.fn(),
    exists: jest.fn(),
    isRedisConnected: jest.fn(() => true),
    healthCheck: jest.fn(() => ({ status: 'healthy', details: {} }))
  }
}));

export {};
