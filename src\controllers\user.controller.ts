import { FastifyRequest, FastifyReply } from 'fastify';
import { AuthenticatedRequest, TransactionStatus } from '../types';
import { UserService } from '../services/user.service';
import { TransactionService } from '../services/transaction.service';
import { ValidationService } from '../services/validation.service';
import { CryptoUtils } from '../utils/crypto';
import { logger } from '../config/logger';
import path from 'path';
import fs from 'fs/promises';

export const getCurrentUser = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'user not authenticated'
    });
  }

  try {
    const user = await UserService.getUserById(request.user.id);

    if (!user) {
      return reply.status(404).send({
        success: false,
        message: 'user not found'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'user profile retrieved successfully',
      data: {
        userProfile: {
          userId: user._id?.toString(),
          email: user.email,
          phoneNumber: user.phone,
          username: user.username,
          firstName: user.first_name,
          lastName: user.last_name,
          fullName: `${user.first_name} ${user.last_name}`,
          profilePicture: user.profile_picture,
          bio: user.bio,
          userRole: user.role,
          isVerified: user.is_verified,
          kycStatus: user.kyc_status,
          walletBalance: user.wallet_balance,
          accountCreated: user.created_at
        },
        addressInfo: user.address ? {
          street: user.address.street,
          city: user.address.city,
          state: user.address.state,
          country: user.address.country,
          postalCode: user.address.postal_code
        } : null,
        agentDetails: user.agent_info ? {
          commissionRate: user.agent_info.commission_rate,
          totalTransactions: user.agent_info.total_transactions,
          totalCommissionEarned: user.agent_info.total_commission_earned,
          isActive: user.agent_info.is_active
        } : null
      }
    });
  } catch (error: any) {
    logger.error('error fetching user profile:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to fetch user profile'
    });
  }
};

export const updateProfile = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const validation = await ValidationService.validateData(request.body, ValidationService.updateProfileSchema);
    
    if (!validation.isValid) {
      return reply.status(400).send({
        success: false,
        message: 'validation failed',
        errors: validation.errors
      });
    }

    const updatedUser = await UserService.updateProfile(request.user.id, validation.data);

    if (!updatedUser) {
      return reply.status(404).send({
        success: false,
        message: 'user not found'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'profile updated successfully',
      data: {
        id: updatedUser._id?.toString(),
        first_name: updatedUser.first_name,
        last_name: updatedUser.last_name,
        phone: updatedUser.phone,
        bio: updatedUser.bio,
        address: updatedUser.address
      }
    });
  } catch (error: any) {
    logger.error('error updating profile:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to update profile'
    });
  }
};

export const updatePassword = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const validation = await ValidationService.validateData(request.body, ValidationService.changePasswordSchema);
    
    if (!validation.isValid) {
      return reply.status(400).send({
        success: false,
        message: 'validation failed',
        errors: validation.errors
      });
    }

    const { currentPassword, newPassword } = validation.data;

    await UserService.changePassword(request.user.id, currentPassword, newPassword);

    return reply.status(200).send({
      success: true,
      message: 'password updated successfully'
    });
  } catch (error: any) {
    logger.error('error updating password:', error);
    
    if (error.message === 'current password is incorrect') {
      return reply.status(400).send({
        success: false,
        message: 'current password is incorrect'
      });
    }

    return reply.status(500).send({
      success: false,
      message: 'failed to update password'
    });
  }
};

export const uploadProfilePicture = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    // handle file upload
    const data = await request.file();

    if (!data) {
      return reply.status(400).send({
        success: false,
        message: 'no file uploaded'
      });
    }

    // validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(data.mimetype)) {
      return reply.status(400).send({
        success: false,
        message: 'invalid file type. only JPEG, PNG, and WebP are allowed'
      });
    }

    // validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    const buffer = await data.toBuffer();
    if (buffer.length > maxSize) {
      return reply.status(400).send({
        success: false,
        message: 'file size too large. maximum size is 5MB'
      });
    }

    // generate unique filename
    const fileExtension = path.extname(data.filename || '.jpg');
    const fileName = `profile_${request.user.id}_${Date.now()}${fileExtension}`;
    const uploadDir = path.join(process.cwd(), 'uploads', 'profiles');
    const filePath = path.join(uploadDir, fileName);

    try {
      await fs.mkdir(uploadDir, { recursive: true });
    } catch (error) {
      // directory might already exist
    }

    await fs.writeFile(filePath, buffer);

    const profilePictureUrl = `/uploads/profiles/${fileName}`;
    const updated = await UserService.updateProfile(request.user.id, {
      profilePicture: profilePictureUrl
    });

    if (!updated) {
      try {
        await fs.unlink(filePath);
      } catch (error) {
        logger.error('failed to cleanup uploaded file:', error);
      }

      return reply.status(500).send({
        success: false,
        message: 'failed to update profile picture'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'profile picture uploaded successfully',
      data: {
        profile_picture: profilePictureUrl
      }
    });
  } catch (error: any) {
    logger.error('error uploading profile picture:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to upload profile picture'
    });
  }
};

export const deleteAccount = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const { password } = request.body as any;

    if (!password) {
      return reply.status(400).send({
        success: false,
        message: 'password required to delete account'
      });
    }

    // get user to verify password
    const user = await UserService.getUserById(request.user.id);
    if (!user) {
      return reply.status(404).send({
        success: false,
        message: 'user not found'
      });
    }

    // verify password
    const isValidPassword = await CryptoUtils.verifyPassword(password, user.password);
    if (!isValidPassword) {
      return reply.status(400).send({
        success: false,
        message: 'invalid password'
      });
    }

    // check if user has active loans or pending transactions
    const userTransactions = await TransactionService.getUserTransactions(request.user.id, {
      status: TransactionStatus.PENDING
    });

    if (userTransactions.total > 0) {
      return reply.status(400).send({
        success: false,
        message: 'cannot delete account with pending transactions'
      });
    }

    // proceed with account deletion
    const deleted = await UserService.deleteAccount(request.user.id);

    if (!deleted) {
      return reply.status(404).send({
        success: false,
        message: 'user not found'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'account deleted successfully'
    });
  } catch (error: any) {
    logger.error('error deleting account:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to delete account'
    });
  }
};

export const searchUsers = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const { search, role, kycStatus, accountStatus, page = 1, limit = 20 } = request.query as any;

    const result = await UserService.searchUsers({
      search,
      role,
      kycStatus,
      accountStatus,
      page: parseInt(page),
      limit: parseInt(limit)
    });

    return reply.status(200).send({
      success: true,
      message: 'users retrieved successfully',
      data: result.users.map(user => ({
        id: user._id?.toString(),
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
        role: user.role,
        kyc_status: user.kyc_status,
        account_status: user.account_status,
        is_verified: user.is_verified,
        created_at: user.created_at
      })),
      pagination: {
        page: result.page,
        limit,
        total: result.total,
        pages: result.pages,
        hasNext: result.page < result.pages,
        hasPrev: result.page > 1
      }
    });
  } catch (error: any) {
    logger.error('error searching users:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to search users'
    });
  }
};

export const verifyEmail = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const { token } = request.params as any;

    if (!token) {
      return reply.status(400).send({
        success: false,
        message: 'verification token required'
      });
    }

    const verified = await UserService.verifyEmail(token);

    if (!verified) {
      return reply.status(400).send({
        success: false,
        message: 'invalid or expired verification token'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'email verified successfully'
    });
  } catch (error: any) {
    logger.error('error verifying email:', error);
    return reply.status(500).send({
      success: false,
      message: 'email verification failed'
    });
  }
};

export const getUserStats = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const user = await UserService.getUserById(request.user.id);

    if (!user) {
      return reply.status(404).send({
        success: false,
        message: 'user not found'
      });
    }

    // get actual transaction stats
    const [allTransactions, sentTransactions] = await Promise.all([
      TransactionService.getUserTransactions(request.user.id, { limit: 1000 }),
      TransactionService.getUserTransactions(request.user.id, {
        type: 'p2p_transfer' as any,
        limit: 1000
      })
    ]);

    const userId = request.user!.id; 

    // calculate sent amount (outgoing transfers)
    const sentAmount = sentTransactions.transactions
      .filter(t => t.user_id.toString() === userId && t.amount > 0)
      .reduce((sum, t) => sum + t.amount, 0);

    // calculate received amount (incoming transfers where user is recipient)
    const receivedAmount = allTransactions.transactions
      .filter(t => t.recipient_id?.toString() === userId && t.amount > 0)
      .reduce((sum, t) => sum + t.amount, 0);

    // calculate transaction counts by status
    const completedTransactions = allTransactions.transactions.filter(t => t.status === 'completed').length;
    const pendingTransactions = allTransactions.transactions.filter(t => t.status === 'pending').length;
    const failedTransactions = allTransactions.transactions.filter(t => t.status === 'failed').length;

    const stats = {
      total_transactions: allTransactions.total,
      completed_transactions: completedTransactions,
      pending_transactions: pendingTransactions,
      failed_transactions: failedTransactions,
      total_sent: Math.round(sentAmount * 100) / 100,
      total_received: Math.round(receivedAmount * 100) / 100,
      net_flow: Math.round((receivedAmount - sentAmount) * 100) / 100,
      wallet_balance: user.wallet_balance,
      kyc_status: user.kyc_status,
      account_status: user.account_status,
      is_verified: user.is_verified,
      account_age_days: Math.floor((Date.now() - user.created_at.getTime()) / (1000 * 60 * 60 * 24)),
      last_transaction_date: allTransactions.transactions.length > 0 ?
        allTransactions.transactions[0]?.created_at : null,
      average_transaction_amount: allTransactions.total > 0 ?
        Math.round((sentAmount / allTransactions.total) * 100) / 100 : 0
    };

    return reply.status(200).send({
      success: true,
      message: 'user stats retrieved',
      data: stats
    });
  } catch (error: any) {
    logger.error('error getting user stats:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to get user stats'
    });
  }
};
