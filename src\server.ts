import Fastify, { FastifyInstance } from 'fastify';
import { config } from './config';
import { logger, requestLogger } from './config/logger';
import { db } from './config/database';
import { redis } from './config/redis';

// Import plugins
import cors from '@fastify/cors';
import helmet from '@fastify/helmet';
import rateLimit from '@fastify/rate-limit';
import jwt from '@fastify/jwt';
import cookie from '@fastify/cookie';
import multipart from '@fastify/multipart';
import staticFiles from '@fastify/static';
import fs from 'fs';

// Import routes
import { healthRoutes } from './routes/health.routes';
import { authRoutes } from './routes/auth.routes';
import { userRoutes } from './routes/user.routes';
import { transferRoutes } from './routes/transfer.routes';
import { agentRoutes } from './routes/agent.routes';
import { loanRoutes } from './routes/loan.routes';
import { savingsRoutes } from './routes/savings.routes';
import { adminRoutes } from './routes/admin.routes';
import { MonitoringMiddleware } from './middleware/monitoring.middleware';

class Server {
  private app: FastifyInstance;

  constructor() {
    this.app = Fastify({
      logger: false, // We use our custom logger
      trustProxy: true,
      bodyLimit: config.upload.maxFileSize,
      requestIdHeader: 'x-request-id',
      requestIdLogLabel: 'requestId'
    });

    this.setupPlugins();
    this.setupRoutes();
    this.setupErrorHandlers();
  }

  private async setupPlugins(): Promise<void> {
    try {
      // Security plugins
      await this.app.register(helmet, {
        contentSecurityPolicy: {
          directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
          },
        },
      });

      await this.app.register(cors, config.cors);

      // Rate limiting
      await this.app.register(rateLimit, {
        max: config.rateLimit.max,
        timeWindow: config.rateLimit.windowMs,
        errorResponseBuilder: (_request, context) => {
          return {
            success: false,
            message: 'Rate limit exceeded',
            error: `Too many requests, retry after ${Math.round(context.ttl / 1000)} seconds`
          };
        }
      });

      // JWT authentication
      await this.app.register(jwt, {
        secret: config.jwt.secret,
        cookie: {
          cookieName: 'token',
          signed: false
        }
      });

      // Cookie support
      await this.app.register(cookie, {
        secret: config.security.cookieSecret,
        parseOptions: {
          httpOnly: true,
          secure: config.server.env === 'production',
          sameSite: 'strict'
        }
      });

      // File upload support
      await this.app.register(multipart, {
        limits: {
          fileSize: config.upload.maxFileSize,
          files: 5
        }
      });

      // Static files - ensure upload directory exists
      if (!fs.existsSync(config.upload.uploadPath)) {
        fs.mkdirSync(config.upload.uploadPath, { recursive: true });
        logger.info(`Created upload directory: ${config.upload.uploadPath}`);
      }

      await this.app.register(staticFiles, {
        root: config.upload.uploadPath,
        prefix: '/uploads/'
      });

      // Enhanced monitoring middleware
      this.app.addHook('onRequest', MonitoringMiddleware.requestTracking);
      this.app.addHook('preHandler', MonitoringMiddleware.auditLogging);
      this.app.addHook('preHandler', MonitoringMiddleware.securityHeaders);
      this.app.addHook('onResponse', MonitoringMiddleware.responseTracking);
      this.app.setErrorHandler(MonitoringMiddleware.errorTracking);

      // Legacy request logging (keeping for compatibility)
      this.app.addHook('onRequest', async (request, _reply) => {
        const startTime = Date.now();
        (request as any).startTime = startTime;

        requestLogger.info('Incoming request', {
          method: request.method,
          url: request.url,
          ip: request.ip,
          userAgent: request.headers['user-agent'],
          requestId: request.id
        });
      });

      // Response logging middleware
      this.app.addHook('onResponse', async (request, reply) => {
        const duration = Date.now() - ((request as any).startTime || Date.now());

        requestLogger.info('Request completed', {
          method: request.method,
          url: request.url,
          statusCode: reply.statusCode,
          duration: `${duration}ms`,
          requestId: request.id
        });
      });

      logger.info('Fastify plugins registered successfully');
    } catch (error) {
      logger.error('Error setting up plugins:', error);
      throw error;
    }
  }

  private setupRoutes(): void {
    // API prefix
    const apiPrefix = `/api/${config.server.apiVersion}`;

    // Health check routes
    this.app.register(healthRoutes, { prefix: `${apiPrefix}/health` });

    // Auth routes
    this.app.register(authRoutes, { prefix: `${apiPrefix}/auth` });

    // User routes
    this.app.register(userRoutes, { prefix: `${apiPrefix}/users` });

    // Transfer routes
    this.app.register(transferRoutes, { prefix: `${apiPrefix}/transfers` });

    // Agent routes
    this.app.register(agentRoutes, { prefix: `${apiPrefix}/agents` });

    // Loan routes
    this.app.register(loanRoutes, { prefix: `${apiPrefix}/loans` });

    // Savings routes
    this.app.register(savingsRoutes, { prefix: `${apiPrefix}/savings` });

    // Admin routes
    this.app.register(adminRoutes, { prefix: `${apiPrefix}/admin` });

    // monitoring routes
    this.app.get('/health', MonitoringMiddleware.healthCheck);
    this.app.get('/metrics', MonitoringMiddleware.getMetrics);

    // Root route
    this.app.get('/', async (_request, _reply) => {
      return {
        success: true,
        message: 'AeTrust Backend API',
        version: config.server.apiVersion,
        environment: config.server.env,
        timestamp: new Date().toISOString()
      };
    });

    // 404 handler
    this.app.setNotFoundHandler(async (request, reply) => {
      reply.status(404).send({
        success: false,
        message: 'Route not found',
        error: `Cannot ${request.method} ${request.url}`
      });
    });

    logger.info('Routes registered successfully');
  }

  private setupErrorHandlers(): void {
    // Global error handler
    this.app.setErrorHandler(async (error, request, reply) => {
      logger.error('Unhandled error:', {
        error: error.message,
        stack: error.stack,
        method: request.method,
        url: request.url,
        requestId: request.id
      });

      const statusCode = error.statusCode || 500;
      const message = config.server.env === 'production' 
        ? 'Internal server error' 
        : error.message;

      reply.status(statusCode).send({
        success: false,
        message,
        ...(config.server.env !== 'production' && { stack: error.stack })
      });
    });

    // Graceful shutdown
    const gracefulShutdown = async (signal: string) => {
      logger.info(`Received ${signal}, starting graceful shutdown...`);
      
      try {
        await this.app.close();
        await db.disconnect();

        // Try to disconnect Redis if connected
        try {
          if (redis.isRedisConnected()) {
            await redis.disconnect();
          }
        } catch (redisError) {
          logger.warn('Redis disconnect error (non-critical):', redisError);
        }

        logger.info('Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error('Error during shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception:', error);
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled rejection at:', promise, 'reason:', reason);
      process.exit(1);
    });
  }

  public async start(): Promise<void> {
    try {
      // Connect to databases
      await db.connect();

      // Try to connect to Redis (optional for development)
      try {
        await redis.connect();
        logger.info('✅ Redis connected successfully');
      } catch (redisError) {
        logger.warn('⚠️ Redis connection failed - continuing without cache:', redisError instanceof Error ? redisError.message : 'Unknown error');
        logger.info('💡 To enable Redis: Start Memurai service or install Redis');
      }

      // Start server
      await this.app.listen({
        port: config.server.port,
        host: config.server.host
      });

      logger.info(`Server started successfully on ${config.server.host}:${config.server.port}`);
      logger.info(`Environment: ${config.server.env}`);
      logger.info(`API Version: ${config.server.apiVersion}`);
      
    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  public getApp(): FastifyInstance {
    return this.app;
  }
}

// Start server if this file is run directly
if (require.main === module) {
  const server = new Server();
  server.start();
}

export { Server };
