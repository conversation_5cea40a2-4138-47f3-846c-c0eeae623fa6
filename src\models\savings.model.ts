import mongoose, { Schema, Document } from 'mongoose';
import { Currency, SavingsType, SavingsStatus } from '../types';

export interface ISavings extends Document {
  _id: mongoose.Types.ObjectId;
  user_id: mongoose.Types.ObjectId;
  account_number: string;
  account_name: string;
  savings_type: SavingsType;
  currency: Currency;
  target_amount?: number;
  target_date?: Date;
  current_balance: number;
  interest_rate: number;
  interest_earned: number;
  status: SavingsStatus;
  auto_save_settings?: {
    enabled: boolean;
    amount: number;
    frequency: 'daily' | 'weekly' | 'monthly';
    next_deduction_date?: Date;
  };
  goal_settings?: {
    goal_name: string;
    goal_description?: string;
    target_amount: number;
    target_date: Date;
    progress_percentage: number;
  };
  withdrawal_restrictions: {
    minimum_balance: number;
    withdrawal_limit_per_day: number;
    withdrawal_limit_per_month: number;
    early_withdrawal_penalty: number;
  };
  transaction_history: {
    transaction_id: mongoose.Types.ObjectId;
    transaction_type: 'deposit' | 'withdrawal' | 'interest' | 'penalty';
    amount: number;
    balance_before: number;
    balance_after: number;
    description: string;
    created_at: Date;
  }[];
  maturity_date?: Date;
  last_interest_calculation: Date;
  created_at: Date;
  updated_at: Date;
}

const savingsSchema = new Schema<ISavings>({
  user_id: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  account_number: {
    type: String,
    required: true,
    unique: true,
    uppercase: true,
    index: true
  },
  account_name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  savings_type: {
    type: String,
    enum: Object.values(SavingsType),
    required: true,
    index: true
  },
  currency: {
    type: String,
    enum: Object.values(Currency),
    required: true
  },
  target_amount: {
    type: Number,
    min: 0,
    set: (v: number) => v ? Math.round(v * 100) / 100 : v
  },
  target_date: {
    type: Date
  },
  current_balance: {
    type: Number,
    required: true,
    default: 0,
    min: 0,
    set: (v: number) => Math.round(v * 100) / 100
  },
  interest_rate: {
    type: Number,
    required: true,
    min: 0,
    max: 1,
    default: 0.05 // 5% annual
  },
  interest_earned: {
    type: Number,
    default: 0,
    min: 0,
    set: (v: number) => Math.round(v * 100) / 100
  },
  status: {
    type: String,
    enum: Object.values(SavingsStatus),
    default: SavingsStatus.ACTIVE,
    index: true
  },
  auto_save_settings: {
    enabled: { type: Boolean, default: false },
    amount: { type: Number, min: 0 },
    frequency: { 
      type: String, 
      enum: ['daily', 'weekly', 'monthly'],
      default: 'monthly'
    },
    next_deduction_date: { type: Date }
  },
  goal_settings: {
    goal_name: { type: String, trim: true, maxlength: 100 },
    goal_description: { type: String, trim: true, maxlength: 500 },
    target_amount: { type: Number, min: 0 },
    target_date: { type: Date },
    progress_percentage: { type: Number, min: 0, max: 100, default: 0 }
  },
  withdrawal_restrictions: {
    minimum_balance: { type: Number, default: 0, min: 0 },
    withdrawal_limit_per_day: { type: Number, default: 1000, min: 0 },
    withdrawal_limit_per_month: { type: Number, default: 10000, min: 0 },
    early_withdrawal_penalty: { type: Number, default: 0.02, min: 0, max: 0.1 }
  },
  transaction_history: [{
    transaction_id: { type: Schema.Types.ObjectId, ref: 'Transaction', required: true },
    transaction_type: { 
      type: String, 
      enum: ['deposit', 'withdrawal', 'interest', 'penalty'],
      required: true 
    },
    amount: { type: Number, required: true },
    balance_before: { type: Number, required: true },
    balance_after: { type: Number, required: true },
    description: { type: String, required: true, trim: true },
    created_at: { type: Date, default: Date.now }
  }],
  maturity_date: {
    type: Date,
    index: true
  },
  last_interest_calculation: {
    type: Date,
    default: Date.now
  },
  created_at: {
    type: Date,
    default: Date.now,
    index: true
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  toJSON: {
    transform: function(_doc: any, ret: any) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// indexes
savingsSchema.index({ user_id: 1, savings_type: 1 });
savingsSchema.index({ status: 1, maturity_date: 1 });
savingsSchema.index({ 'auto_save_settings.enabled': 1, 'auto_save_settings.next_deduction_date': 1 });
savingsSchema.index({ created_at: -1 });

// pre-save middleware
savingsSchema.pre('save', function(next) {
  this.updated_at = new Date();
  
  // calculate goal progress if goal is set
  if (this.goal_settings?.target_amount && this.goal_settings.target_amount > 0) {
    this.goal_settings.progress_percentage = Math.min(
      (this.current_balance / this.goal_settings.target_amount) * 100,
      100
    );
  }
  
  next();
});

// virtual for account display name
savingsSchema.virtual('display_name').get(function() {
  return `${this.account_name} (${this.account_number})`;
});

// virtual for days to maturity
savingsSchema.virtual('days_to_maturity').get(function() {
  if (!this.maturity_date) return null;
  
  const today = new Date();
  const diffTime = this.maturity_date.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return diffDays > 0 ? diffDays : 0;
});

// virtual for projected interest
savingsSchema.virtual('projected_annual_interest').get(function() {
  return this.current_balance * this.interest_rate;
});

// virtual for goal achievement status
savingsSchema.virtual('goal_status').get(function() {
  if (!this.goal_settings?.target_amount) return null;
  
  const progress = this.goal_settings.progress_percentage;
  if (progress >= 100) return 'achieved';
  if (progress >= 75) return 'almost_there';
  if (progress >= 50) return 'halfway';
  if (progress >= 25) return 'getting_started';
  return 'just_started';
});

// methods
savingsSchema.methods.deposit = function(amount: number, description: string, transactionId: mongoose.Types.ObjectId): void {
  const balanceBefore = this.current_balance;
  this.current_balance += amount;
  
  this.transaction_history.push({
    transaction_id: transactionId,
    transaction_type: 'deposit',
    amount,
    balance_before: balanceBefore,
    balance_after: this.current_balance,
    description,
    created_at: new Date()
  });
};

savingsSchema.methods.withdraw = function(amount: number, description: string, transactionId: mongoose.Types.ObjectId): { 
  success: boolean; 
  message: string;
  penalty?: number;
} {
  // check minimum balance
  if (this.current_balance - amount < this.withdrawal_restrictions.minimum_balance) {
    return { 
      success: false, 
      message: 'withdrawal would violate minimum balance requirement' 
    };
  }
  
  // check if sufficient balance
  if (this.current_balance < amount) {
    return { 
      success: false, 
      message: 'insufficient balance' 
    };
  }
  
  let penalty = 0;
  let finalAmount = amount;
  
  // check for early withdrawal penalty (for fixed deposits)
  if (this.savings_type === SavingsType.FIXED_DEPOSIT && this.maturity_date && new Date() < this.maturity_date) {
    penalty = amount * this.withdrawal_restrictions.early_withdrawal_penalty;
    finalAmount = amount + penalty;
    
    if (this.current_balance < finalAmount) {
      return { 
        success: false, 
        message: 'insufficient balance to cover withdrawal and penalty' 
      };
    }
  }
  
  const balanceBefore = this.current_balance;
  this.current_balance -= finalAmount;
  
  // record withdrawal
  this.transaction_history.push({
    transaction_id: transactionId,
    transaction_type: 'withdrawal',
    amount,
    balance_before: balanceBefore,
    balance_after: this.current_balance,
    description,
    created_at: new Date()
  });
  
  // record penalty if applicable
  if (penalty > 0) {
    this.transaction_history.push({
      transaction_id: transactionId,
      transaction_type: 'penalty',
      amount: penalty,
      balance_before: balanceBefore - amount,
      balance_after: this.current_balance,
      description: 'early withdrawal penalty',
      created_at: new Date()
    });
  }
  
  const result: { success: boolean; message: string; penalty?: number } = {
    success: true,
    message: 'withdrawal successful'
  };

  if (penalty > 0) {
    result.penalty = penalty;
  }

  return result;
};

savingsSchema.methods.calculateInterest = function(): number {
  const now = new Date();
  const lastCalculation = this.last_interest_calculation;
  const daysSinceLastCalculation = Math.floor((now.getTime() - lastCalculation.getTime()) / (1000 * 60 * 60 * 24));
  
  if (daysSinceLastCalculation < 1) return 0;
  
  // calculate daily interest
  const dailyRate = this.interest_rate / 365;
  const interest = this.current_balance * dailyRate * daysSinceLastCalculation;
  
  return Math.round(interest * 100) / 100;
};

savingsSchema.methods.applyInterest = function(transactionId: mongoose.Types.ObjectId): number {
  const interest = this.calculateInterest();
  
  if (interest > 0) {
    const balanceBefore = this.current_balance;
    this.current_balance += interest;
    this.interest_earned += interest;
    this.last_interest_calculation = new Date();
    
    this.transaction_history.push({
      transaction_id: transactionId,
      transaction_type: 'interest',
      amount: interest,
      balance_before: balanceBefore,
      balance_after: this.current_balance,
      description: 'interest earned',
      created_at: new Date()
    });
  }
  
  return interest;
};

// static methods
savingsSchema.statics.findUserSavings = function(userId: string, savingsType?: SavingsType) {
  const query: any = { user_id: userId };
  if (savingsType) query.savings_type = savingsType;
  return this.find(query).sort({ created_at: -1 });
};

savingsSchema.statics.findMaturedAccounts = function() {
  return this.find({
    savings_type: SavingsType.FIXED_DEPOSIT,
    status: SavingsStatus.ACTIVE,
    maturity_date: { $lte: new Date() }
  });
};

savingsSchema.statics.findAutoSaveAccounts = function() {
  return this.find({
    'auto_save_settings.enabled': true,
    'auto_save_settings.next_deduction_date': { $lte: new Date() },
    status: SavingsStatus.ACTIVE
  });
};

export const Savings = mongoose.model<ISavings>('Savings', savingsSchema);
