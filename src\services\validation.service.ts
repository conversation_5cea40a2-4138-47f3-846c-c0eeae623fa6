import Joi from 'joi';
import { ValidationUtils } from '../utils/validation';

export class ValidationService {
  // auth validation schemas
  static registerSchema = Joi.object({
    email: Joi.string().email().required().messages({
      'string.email': 'please enter a valid email',
      'any.required': 'email is required'
    }),
    password: Joi.string().min(8).required().messages({
      'string.min': 'password must be at least 8 characters',
      'any.required': 'password is required'
    }),
    firstName: Joi.string().min(2).max(50).required().messages({
      'string.min': 'first name too short',
      'string.max': 'first name too long',
      'any.required': 'first name required'
    }),
    lastName: Joi.string().min(2).max(50).required().messages({
      'string.min': 'last name too short', 
      'string.max': 'last name too long',
      'any.required': 'last name required'
    }),
    phone: Joi.string().pattern(/^\+[1-9]\d{1,14}$/).required().messages({
      'string.pattern.base': 'phone number format invalid',
      'any.required': 'phone number required'
    }),
    dateOfBirth: Joi.date().max('now').required().messages({
      'date.max': 'invalid birth date',
      'any.required': 'date of birth required'
    })
  });

  static loginSchema = Joi.object({
    email: Joi.string().email().required().messages({
      'string.email': 'invalid email format',
      'any.required': 'email required'
    }),
    password: Joi.string().required().messages({
      'any.required': 'password required'
    }),
    twoFactorCode: Joi.string().length(6).optional().messages({
      'string.length': '2FA code must be 6 digits'
    })
  });

  static refreshTokenSchema = Joi.object({
    refreshToken: Joi.string().required().messages({
      'any.required': 'refresh token required'
    })
  });

  static twoFACodeSchema = Joi.object({
    code: Joi.string().min(6).max(8).required().messages({
      'string.min': 'code too short',
      'string.max': 'code too long',
      'any.required': 'verification code required'
    })
  });

  // user validation schemas
  static updateProfileSchema = Joi.object({
    firstName: Joi.string().min(2).max(50).optional(),
    lastName: Joi.string().min(2).max(50).optional(),
    phone: Joi.string().pattern(/^\+[1-9]\d{1,14}$/).optional(),
    bio: Joi.string().max(500).optional().allow(''),
    address: Joi.object({
      street: Joi.string().max(100).optional(),
      city: Joi.string().max(50).optional(),
      state: Joi.string().max(50).optional(),
      country: Joi.string().length(2).optional(),
      postalCode: Joi.string().max(20).optional()
    }).optional()
  });

  static changePasswordSchema = Joi.object({
    currentPassword: Joi.string().required().messages({
      'any.required': 'current password required'
    }),
    newPassword: Joi.string().min(8).required().messages({
      'string.min': 'new password must be at least 8 characters',
      'any.required': 'new password required'
    }),
    confirmPassword: Joi.string().valid(Joi.ref('newPassword')).required().messages({
      'any.only': 'passwords do not match',
      'any.required': 'password confirmation required'
    })
  });

  // transaction validation schemas
  static transferSchema = Joi.object({
    recipientId: Joi.string().required().messages({
      'any.required': 'recipient required'
    }),
    amount: Joi.number().positive().precision(2).required().messages({
      'number.positive': 'amount must be positive',
      'number.precision': 'amount can have max 2 decimal places',
      'any.required': 'amount required'
    }),
    currency: Joi.string().valid('USD', 'EUR', 'RWF', 'KES').required().messages({
      'any.only': 'unsupported currency',
      'any.required': 'currency required'
    }),
    description: Joi.string().max(200).optional().allow(''),
    pin: Joi.string().length(4).pattern(/^\d+$/).required().messages({
      'string.length': 'PIN must be 4 digits',
      'string.pattern.base': 'PIN must contain only numbers',
      'any.required': 'transaction PIN required'
    })
  });

  static depositSchema = Joi.object({
    amount: Joi.number().positive().precision(2).min(1).required().messages({
      'number.positive': 'amount must be positive',
      'number.min': 'minimum deposit is 1',
      'any.required': 'amount required'
    }),
    currency: Joi.string().valid('USD', 'EUR', 'RWF', 'KES').required(),
    paymentMethod: Joi.string().valid('card', 'bank_transfer', 'mobile_money').required().messages({
      'any.only': 'invalid payment method',
      'any.required': 'payment method required'
    })
  });

  static remittanceSchema = Joi.object({
    recipientCountry: Joi.string().length(2).uppercase().required().messages({
      'string.length': 'country code must be 2 characters',
      'any.required': 'recipient country required'
    }),
    recipientName: Joi.string().min(2).max(100).required().messages({
      'string.min': 'recipient name must be at least 2 characters',
      'string.max': 'recipient name cannot exceed 100 characters',
      'any.required': 'recipient name required'
    }),
    recipientPhone: Joi.string().pattern(/^\+?[1-9]\d{1,14}$/).required().messages({
      'string.pattern.base': 'invalid phone number format',
      'any.required': 'recipient phone required'
    }),
    amount: Joi.number().positive().precision(2).min(10).max(50000).required().messages({
      'number.positive': 'amount must be positive',
      'number.min': 'minimum remittance amount is 10',
      'number.max': 'maximum remittance amount is 50000',
      'any.required': 'amount required'
    }),
    sourceCurrency: Joi.string().valid('USD', 'EUR', 'RWF', 'KES').required().messages({
      'any.only': 'unsupported source currency',
      'any.required': 'source currency required'
    }),
    targetCurrency: Joi.string().valid('USD', 'EUR', 'RWF', 'KES').required().messages({
      'any.only': 'unsupported target currency',
      'any.required': 'target currency required'
    }),
    deliveryMethod: Joi.string().valid('bank', 'mobile_money', 'cash_pickup').required().messages({
      'any.only': 'invalid delivery method',
      'any.required': 'delivery method required'
    }),
    bankDetails: Joi.when('deliveryMethod', {
      is: 'bank',
      then: Joi.object({
        bankName: Joi.string().required(),
        accountNumber: Joi.string().required(),
        accountName: Joi.string().required(),
        swiftCode: Joi.string().optional(),
        routingNumber: Joi.string().optional()
      }).required(),
      otherwise: Joi.optional()
    }),
    description: Joi.string().max(200).optional().allow(''),
    pin: Joi.string().length(4).pattern(/^\d+$/).required().messages({
      'string.length': 'PIN must be 4 digits',
      'string.pattern.base': 'PIN must contain only numbers',
      'any.required': 'transaction PIN required'
    })
  });

  // kyc validation schemas
  static kycSubmissionSchema = Joi.object({
    nationalId: Joi.string().pattern(/^\d{16}$/).required().messages({
      'string.pattern.base': 'national ID must be 16 digits',
      'any.required': 'national ID required'
    }),
    documentType: Joi.string().valid('passport', 'national_id', 'driving_license').required(),
    documentNumber: Joi.string().min(5).max(20).required().messages({
      'string.min': 'document number too short',
      'string.max': 'document number too long',
      'any.required': 'document number required'
    }),
    occupation: Joi.string().max(100).required().messages({
      'any.required': 'occupation required'
    }),
    sourceOfIncome: Joi.string().valid('employment', 'business', 'investment', 'other').required()
  });

  // loan validation schemas
  static loanApplicationSchema = Joi.object({
    amount: Joi.number().positive().min(1000).max(50000).required().messages({
      'number.min': 'minimum loan amount is 1000',
      'number.max': 'maximum loan amount is 50000',
      'any.required': 'loan amount required'
    }),
    purpose: Joi.string().valid('business', 'personal', 'education', 'emergency').required(),
    duration: Joi.number().integer().min(1).max(24).required().messages({
      'number.min': 'minimum duration is 1 month',
      'number.max': 'maximum duration is 24 months',
      'any.required': 'loan duration required'
    }),
    monthlyIncome: Joi.number().positive().required().messages({
      'number.positive': 'monthly income must be positive',
      'any.required': 'monthly income required'
    })
  });

  // pagination schema
  static paginationSchema = Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    sort: Joi.string().optional(),
    order: Joi.string().valid('asc', 'desc').default('desc')
  });

  static async validateData(data: any, schema: Joi.ObjectSchema) {
    try {
      const { error, value } = schema.validate(data, {
        abortEarly: false,
        stripUnknown: true,
        convert: true
      });

      if (error) {
        const errors = error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value
        }));

        return {
          isValid: false,
          errors,
          data: null
        };
      }

      return {
        isValid: true,
        errors: null,
        data: value
      };
    } catch (err) {
      return {
        isValid: false,
        errors: [{ field: 'general', message: 'validation error' }],
        data: null
      };
    }
  }

  static sanitizeInput(data: any) {
    return ValidationUtils.sanitizeUserInput(data);
  }
}
