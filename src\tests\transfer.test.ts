import { FastifyInstance } from 'fastify';
import { buildApp } from '../server';

describe('Transfer API', () => {
  let app: FastifyInstance;
  let authToken: string;

  beforeAll(async () => {
    app = buildApp();
    await app.ready();

    // login to get auth token
    const loginResponse = await app.inject({
      method: 'POST',
      url: '/api/v1/auth/login',
      payload: {
        email: '<EMAIL>',
        password: 'Test123!@#'
      }
    });

    const loginBody = JSON.parse(loginResponse.body);
    authToken = loginBody.data.authSession.accessToken;
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /api/v1/transfers/send', () => {
    it('should send money successfully', async () => {
      const transferData = {
        recipientEmail: '<EMAIL>',
        amount: 100,
        currency: 'USD',
        description: 'Test transfer',
        pin: '1234'
      };

      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/transfers/send',
        headers: {
          authorization: `Bearer ${authToken}`
        },
        payload: transferData
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.message).toBe('money sent successfully');
      expect(body.data.transferDetails).toHaveProperty('transferId');
      expect(body.data.transferDetails).toHaveProperty('transactionRef');
      expect(body.data.transferDetails.amount).toBe(100);
      expect(body.data.transferDetails.__typename).toBe('Transfer');
      expect(body.data.recipientInfo.__typename).toBe('TransferRecipient');
    });

    it('should return validation error for invalid amount', async () => {
      const transferData = {
        recipientEmail: '<EMAIL>',
        amount: -100,
        currency: 'USD',
        description: 'Test transfer',
        pin: '1234'
      };

      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/transfers/send',
        headers: {
          authorization: `Bearer ${authToken}`
        },
        payload: transferData
      });

      expect(response.statusCode).toBe(400);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.message).toBe('validation failed');
    });
  });

  describe('POST /api/v1/transfers/remittance', () => {
    it('should initiate remittance successfully', async () => {
      const remittanceData = {
        recipientCountry: 'RW',
        recipientName: 'John Doe',
        recipientPhone: '+250*********',
        amount: 500,
        sourceCurrency: 'USD',
        targetCurrency: 'RWF',
        deliveryMethod: 'mobile_money',
        description: 'Family support'
      };

      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/transfers/remittance',
        headers: {
          authorization: `Bearer ${authToken}`
        },
        payload: remittanceData
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.message).toBe('remittance initiated successfully');
      expect(body.data.remittanceTransaction).toHaveProperty('remittanceId');
      expect(body.data.remittanceTransaction).toHaveProperty('trackingCode');
      expect(body.data.remittanceTransaction.__typename).toBe('RemittanceTransaction');
      expect(body.data.recipientDetails.__typename).toBe('RemittanceRecipient');
      expect(body.data.exchangeDetails.__typename).toBe('ExchangeInfo');
    });
  });

  describe('POST /api/v1/transfers/bill-payment', () => {
    it('should pay bill successfully', async () => {
      const billData = {
        billType: 'electricity',
        provider: 'EUCL',
        accountNumber: '*********',
        amount: 50,
        currency: 'RWF'
      };

      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/transfers/bill-payment',
        headers: {
          authorization: `Bearer ${authToken}`
        },
        payload: billData
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.message).toBe('bill payment successful');
      expect(body.data.paymentTransaction).toHaveProperty('paymentId');
      expect(body.data.paymentTransaction).toHaveProperty('confirmationCode');
      expect(body.data.paymentTransaction.__typename).toBe('BillPaymentTransaction');
      expect(body.data.billDetails.__typename).toBe('BillDetails');
    });
  });

  describe('GET /api/v1/transfers/history', () => {
    it('should get transfer history successfully', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/transfers/history?page=1&limit=10',
        headers: {
          authorization: `Bearer ${authToken}`
        }
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.message).toBe('transfer history retrieved');
      expect(body.data).toHaveProperty('transfers');
      expect(body.data).toHaveProperty('pagination');
      expect(body.data.pagination.__typename).toBe('Pagination');
    });
  });

  describe('GET /api/v1/transfers/exchange-rates', () => {
    it('should get exchange rates successfully', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/transfers/exchange-rates'
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.message).toBe('exchange rates retrieved');
      expect(body.data).toHaveProperty('rates');
      expect(body.data.__typename).toBe('ExchangeRates');
      expect(body.metadata).toHaveProperty('cache_ttl');
    });

    it('should get specific exchange rate', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/transfers/exchange-rates?from=USD&to=RWF'
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.data.rates).toHaveProperty('USD');
      expect(body.data.rates.USD).toHaveProperty('RWF');
    });
  });

  describe('GET /api/v1/transfers/bill-providers', () => {
    it('should get bill providers successfully', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/transfers/bill-providers'
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.message).toBe('bill providers retrieved');
      expect(body.data).toHaveProperty('providers');
      expect(body.data.__typename).toBe('BillProviderList');
      expect(Array.isArray(body.data.providers)).toBe(true);
    });

    it('should filter bill providers by country', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/transfers/bill-providers?country=RW&bill_type=electricity'
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.metadata.filters.country).toBe('RW');
      expect(body.metadata.filters.bill_type).toBe('electricity');
    });
  });

  describe('GET /api/v1/transfers/health', () => {
    it('should return transfer service health', async () => {
      const response = await app.inject({
        method: 'GET',
        url: '/api/v1/transfers/health'
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.message).toBe('transfer service running');
      expect(body.data.status).toBe('ok');
      expect(body.data.services).toContain('p2p');
      expect(body.data.services).toContain('remittance');
      expect(body.data.services).toContain('bill_payment');
      expect(body.data.__typename).toBe('TransferServiceHealth');
    });
  });
});
