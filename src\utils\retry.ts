import { logger } from '../config/logger';

export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  jitter: boolean;
  retryableErrors?: string[];
}

export class RetryableError extends Error {
  constructor(message: string, public readonly shouldRetry: boolean = true) {
    super(message);
    this.name = 'RetryableError';
  }
}

export async function withRetry<T>(
  operation: () => Promise<T>,
  config: Partial<RetryConfig> = {},
  operationName?: string
): Promise<T> {
  const finalConfig: RetryConfig = {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 30000,
    backoffMultiplier: 2,
    jitter: true,
    ...config
  };

  let lastError: Error = new Error('unknown error');
  
  for (let attempt = 1; attempt <= finalConfig.maxAttempts; attempt++) {
    try {
      const result = await operation();
      
      if (attempt > 1) {
        logger.info('retry operation succeeded', {
          operationName,
          attempt,
          totalAttempts: finalConfig.maxAttempts
        });
      }
      
      return result;
    } catch (error: any) {
      lastError = error;
      
      // check if error is retryable
      if (!isRetryableError(error, finalConfig.retryableErrors)) {
        logger.error('non-retryable error, stopping retry', {
          operationName,
          attempt,
          error: error.message
        });
        throw error;
      }
      
      if (attempt === finalConfig.maxAttempts) {
        logger.error('max retry attempts reached', {
          operationName,
          attempt,
          maxAttempts: finalConfig.maxAttempts,
          error: error.message
        });
        break;
      }
      
      const delay = calculateDelay(attempt, finalConfig);
      
      logger.warn('operation failed, retrying', {
        operationName,
        attempt,
        nextAttempt: attempt + 1,
        delayMs: delay,
        error: error.message
      });
      
      await sleep(delay);
    }
  }
  
  throw lastError;
}

function isRetryableError(error: Error, retryableErrors?: string[]): boolean {
  // if it's explicitly marked as retryable
  if (error instanceof RetryableError) {
    return error.shouldRetry;
  }
  
  // check against configured retryable errors
  if (retryableErrors) {
    return retryableErrors.some(retryableError => 
      error.message.toLowerCase().includes(retryableError.toLowerCase())
    );
  }
  
  // default retryable conditions
  const retryableConditions = [
    'timeout',
    'network',
    'connection',
    'econnreset',
    'enotfound',
    'econnrefused',
    'socket hang up',
    'request timeout',
    'service unavailable',
    'internal server error',
    'bad gateway',
    'gateway timeout'
  ];
  
  const errorMessage = error.message.toLowerCase();
  return retryableConditions.some(condition => errorMessage.includes(condition));
}

function calculateDelay(attempt: number, config: RetryConfig): number {
  // exponential backoff: baseDelay * (backoffMultiplier ^ (attempt - 1))
  let delay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1);
  
  // cap at max delay
  delay = Math.min(delay, config.maxDelay);
  
  // add jitter to prevent thundering herd
  if (config.jitter) {
    const jitterAmount = delay * 0.1; // 10% jitter
    delay += (Math.random() - 0.5) * 2 * jitterAmount;
  }
  
  return Math.max(delay, 0);
}

function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// specialized retry functions for common scenarios
export async function withDatabaseRetry<T>(
  operation: () => Promise<T>,
  operationName?: string
): Promise<T> {
  return withRetry(operation, {
    maxAttempts: 3,
    baseDelay: 500,
    maxDelay: 5000,
    retryableErrors: [
      'connection',
      'timeout',
      'network',
      'mongo',
      'duplicate key'
    ]
  }, operationName);
}

export async function withExternalApiRetry<T>(
  operation: () => Promise<T>,
  operationName?: string
): Promise<T> {
  return withRetry(operation, {
    maxAttempts: 5,
    baseDelay: 1000,
    maxDelay: 30000,
    backoffMultiplier: 2,
    retryableErrors: [
      'timeout',
      'network',
      'rate limit',
      'service unavailable',
      'internal server error',
      'bad gateway',
      'gateway timeout'
    ]
  }, operationName);
}

export async function withPaymentRetry<T>(
  operation: () => Promise<T>,
  operationName?: string
): Promise<T> {
  return withRetry(operation, {
    maxAttempts: 3,
    baseDelay: 2000,
    maxDelay: 10000,
    retryableErrors: [
      'timeout',
      'network',
      'service unavailable',
      'processing error'
    ]
  }, operationName);
}
