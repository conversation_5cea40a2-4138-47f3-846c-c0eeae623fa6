import { FastifyInstance } from 'fastify';
import { buildApp } from '../server';

describe('Authentication API', () => {
  let app: FastifyInstance;

  beforeAll(async () => {
    app = buildApp();
    await app.ready();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /api/v1/auth/register', () => {
    it('should register a new user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'Test123!@#',
        firstName: 'John',
        lastName: 'Doe',
        phone: '+**********',
        dateOfBirth: '1990-01-01'
      };

      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/auth/register',
        payload: userData
      });

      expect(response.statusCode).toBe(201);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.message).toBe('account created successfully');
      expect(body.data.newUser).toHaveProperty('userId');
      expect(body.data.newUser.email).toBe(userData.email);
      expect(body.data.newUser.__typename).toBe('NewUserAccount');
    });

    it('should return validation error for invalid email', async () => {
      const userData = {
        email: 'invalid-email',
        password: 'Test123!@#',
        firstName: 'John',
        lastName: 'Doe',
        phone: '+**********',
        dateOfBirth: '1990-01-01'
      };

      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/auth/register',
        payload: userData
      });

      expect(response.statusCode).toBe(400);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.message).toBe('validation failed');
      expect(body.errors).toBeDefined();
    });
  });

  describe('POST /api/v1/auth/login', () => {
    it('should login user successfully', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'Test123!@#'
      };

      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/auth/login',
        payload: loginData
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.message).toBe('login successful');
      expect(body.data.authSession).toHaveProperty('accessToken');
      expect(body.data.authSession).toHaveProperty('refreshToken');
      expect(body.data.userInfo).toHaveProperty('userId');
      expect(body.data.userInfo.__typename).toBe('AuthenticatedUser');
    });

    it('should return error for invalid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/auth/login',
        payload: loginData
      });

      expect(response.statusCode).toBe(401);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(false);
      expect(body.message).toBe('invalid credentials');
    });
  });

  describe('POST /api/v1/auth/logout', () => {
    it('should logout user successfully', async () => {
      // first login to get token
      const loginResponse = await app.inject({
        method: 'POST',
        url: '/api/v1/auth/login',
        payload: {
          email: '<EMAIL>',
          password: 'Test123!@#'
        }
      });

      const loginBody = JSON.parse(loginResponse.body);
      const token = loginBody.data.authSession.accessToken;

      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/auth/logout',
        headers: {
          authorization: `Bearer ${token}`
        }
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.message).toBe('logged out successfully');
    });
  });

  describe('POST /api/v1/auth/2fa/setup', () => {
    it('should setup 2FA successfully', async () => {
      // login first
      const loginResponse = await app.inject({
        method: 'POST',
        url: '/api/v1/auth/login',
        payload: {
          email: '<EMAIL>',
          password: 'Test123!@#'
        }
      });

      const loginBody = JSON.parse(loginResponse.body);
      const token = loginBody.data.authSession.accessToken;

      const response = await app.inject({
        method: 'POST',
        url: '/api/v1/auth/2fa/setup',
        headers: {
          authorization: `Bearer ${token}`
        }
      });

      expect(response.statusCode).toBe(200);
      
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.message).toBe('2FA setup generated');
      expect(body.data).toHaveProperty('qrCode');
      expect(body.data).toHaveProperty('backupCodes');
    });
  });
});
