# Memurai (Redis for Windows) Setup Guide

## 🚀 Quick Start

You've already downloaded Memurai! Now let's get it running.

## 📋 Starting Memurai

### Option 1: Start as Windows Service (Recommended)
```cmd
# Open Command Prompt as Administrator
# Start Memurai service
net start Memurai

# Check if it's running
netstat -an | findstr :6379
```

### Option 2: Start Manually
```cmd
# Navigate to Memurai installation directory (usually):
cd "C:\Program Files\Memurai"

# Start Memurai server
memurai-server.exe

# Or with custom config:
memurai-server.exe memurai.conf
```

### Option 3: Start from PowerShell
```powershell
# Start service
Start-Service -Name "Memurai"

# Check status
Get-Service -Name "Memurai"
```

## 🔧 Configuration

### Default Memurai Configuration
- **Host**: localhost (127.0.0.1)
- **Port**: 6379
- **Password**: None (default)
- **Database**: 0

### Your .env Configuration
Make sure your `.env` file has:
```env
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
```

## ✅ Verify Memurai is Running

### Method 1: Command Line Test
```cmd
# Test connection (if you have redis-cli)
redis-cli ping
# Should return: PONG

# Or test with telnet
telnet localhost 6379
# Type: ping
# Should return: +PONG
```

### Method 2: PowerShell Test
```powershell
# Test if port is open
Test-NetConnection -ComputerName localhost -Port 6379
# Should show: TcpTestSucceeded : True
```

### Method 3: Check Windows Services
```cmd
# Check service status
sc query Memurai

# Or in PowerShell
Get-Service Memurai
```

## 🛠️ Troubleshooting

### Issue 1: Service Won't Start
```cmd
# Check if port 6379 is already in use
netstat -ano | findstr :6379

# If something else is using it, kill the process:
taskkill /PID <process_id> /F

# Then start Memurai
net start Memurai
```

### Issue 2: Permission Denied
```cmd
# Run Command Prompt as Administrator
# Right-click Command Prompt → "Run as administrator"
net start Memurai
```

### Issue 3: Service Not Found
```cmd
# Install Memurai as service (run as Administrator)
cd "C:\Program Files\Memurai"
memurai-server.exe --service-install

# Then start it
net start Memurai
```

### Issue 4: Connection Refused
1. Check if Memurai is actually running:
   ```cmd
   netstat -an | findstr :6379
   ```

2. Check Windows Firewall:
   ```cmd
   # Allow Memurai through firewall
   netsh advfirewall firewall add rule name="Memurai" dir=in action=allow protocol=TCP localport=6379
   ```

3. Restart Memurai:
   ```cmd
   net stop Memurai
   net start Memurai
   ```

## 🔄 Alternative: Use Docker Redis

If Memurai is giving you trouble, you can use Docker instead:

```cmd
# Pull Redis image
docker pull redis:alpine

# Run Redis container
docker run -d --name redis -p 6379:6379 redis:alpine

# Test connection
docker exec redis redis-cli ping
```

## 🎯 Quick Test Script

Create a file `test-redis.js`:
```javascript
const redis = require('redis');
const client = redis.createClient({
  url: 'redis://localhost:6379'
});

client.on('error', (err) => console.log('Redis Error:', err));
client.on('connect', () => console.log('✅ Redis Connected!'));

client.connect().then(() => {
  return client.ping();
}).then((result) => {
  console.log('Ping result:', result);
  client.quit();
}).catch((err) => {
  console.log('❌ Connection failed:', err.message);
});
```

Run it:
```cmd
node test-redis.js
```

## 🚀 Start Your Application

Once Memurai is running, start your AeTrust backend:

```cmd
# Method 1: Use the startup script
scripts\start-windows.bat

# Method 2: Manual start
npm run dev

# Method 3: PowerShell script
powershell -ExecutionPolicy Bypass -File scripts\start-windows.ps1
```

## 📊 Success Indicators

When everything is working, you should see:
```
✅ Redis connected successfully
🚀 Server running on http://localhost:3000
```

And when you visit `http://localhost:3000/health`, you should see Redis status as healthy.

## 🆘 Still Having Issues?

1. **Restart your computer** - Sometimes Windows services need a fresh start
2. **Check antivirus** - Some antivirus software blocks Redis/Memurai
3. **Use Docker Redis** - As a fallback option
4. **Run without Redis** - The app will work without caching (just slower)

## 💡 Pro Tips

1. **Auto-start**: Set Memurai service to start automatically:
   ```cmd
   sc config Memurai start= auto
   ```

2. **Monitor**: Use Windows Task Manager to monitor Memurai memory usage

3. **Logs**: Check Memurai logs in the installation directory for errors

4. **Performance**: For development, default settings are fine. For production, tune the configuration.

## 🎉 You're Ready!

Once you see "Redis connected successfully" in your application logs, you're all set! Your AeTrust backend now has full caching capabilities. 🚀
