import { Wallet, IWallet } from '../models/wallet.model';
import { Transaction, ITransaction } from '../models/transaction.model';
import { WalletType, WalletStatus, Currency, TransactionType, TransactionStatus } from '../types';
import { CryptoUtils } from '../utils/crypto';
import { logger } from '../config/logger';
import mongoose from 'mongoose';

export class WalletService {
  static async createWallet(data: {
    userId: string;
    currency: Currency;
    walletType?: WalletType;
    isDefault?: boolean;
  }): Promise<IWallet> {
    try {
      // check if user already has a default wallet for this currency
      if (data.isDefault) {
        await Wallet.updateMany(
          { user_id: data.userId, currency: data.currency },
          { $set: { is_default: false } }
        );
      }

      const wallet = new Wallet({
        user_id: data.userId,
        currency: data.currency,
        wallet_type: data.walletType || WalletType.MAIN,
        wallet_address: CryptoUtils.generateTransactionRef('WALLET'),
        is_default: data.isDefault || false,
        status: WalletStatus.ACTIVE
      });

      await wallet.save();

      logger.info('wallet created successfully', {
        walletId: wallet._id,
        userId: data.userId,
        currency: data.currency,
        type: wallet.wallet_type
      });

      return wallet;
    } catch (error: any) {
      logger.error('error creating wallet:', error);
      throw error;
    }
  }

  static async getUserWallets(userId: string, currency?: Currency): Promise<IWallet[]> {
    try {
      const query: any = { 
        user_id: userId, 
        status: { $ne: WalletStatus.CLOSED } 
      };
      
      if (currency) {
        query.currency = currency;
      }

      const wallets = await Wallet.find(query)
        .sort({ is_default: -1, created_at: 1 });

      return wallets;
    } catch (error: any) {
      logger.error('error getting user wallets:', error);
      throw error;
    }
  }

  static async getWalletById(walletId: string): Promise<IWallet | null> {
    try {
      if (!mongoose.Types.ObjectId.isValid(walletId)) {
        return null;
      }

      const wallet = await Wallet.findOne({
        _id: walletId,
        status: { $ne: WalletStatus.CLOSED }
      });

      return wallet;
    } catch (error: any) {
      logger.error('error getting wallet by id:', error);
      return null;
    }
  }

  static async getDefaultWallet(userId: string, currency: Currency): Promise<IWallet | null> {
    try {
      let wallet = await Wallet.findOne({
        user_id: userId,
        currency,
        is_default: true,
        status: WalletStatus.ACTIVE
      });

      // if no default wallet, create one
      if (!wallet) {
        return await this.createWallet({
          userId,
          currency,
          walletType: WalletType.MAIN,
          isDefault: true
        });
      }

      return wallet;
    } catch (error: any) {
      logger.error('error getting default wallet:', error);
      throw error;
    }
  }

  static async creditWallet(data: {
    walletId: string;
    amount: number;
    description?: string;
    transactionType?: TransactionType;
    externalRef?: string;
    metadata?: any;
  }): Promise<{ wallet: IWallet; transaction: ITransaction }> {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();

      const wallet = await Wallet.findOne({
        _id: data.walletId,
        status: WalletStatus.ACTIVE
      }).session(session);

      if (!wallet) {
        throw new Error('wallet not found or inactive');
      }

      if (!(wallet as any).canCredit()) {
        throw new Error('wallet cannot be credited');
      }

      const balanceBefore = wallet.balance;
      
      // update wallet balance
      (wallet as any).updateBalance(data.amount, 'credit');
      await wallet.save({ session });

      // create transaction record
      const transaction = new Transaction({
        transaction_ref: CryptoUtils.generateTransactionRef('TXN'),
        user_id: wallet.user_id,
        wallet_id: wallet._id,
        type: data.transactionType || TransactionType.DEPOSIT,
        status: TransactionStatus.COMPLETED,
        amount: data.amount,
        currency: wallet.currency,
        description: data.description || 'wallet credit',
        external_reference: data.externalRef,
        balance_before: balanceBefore,
        balance_after: wallet.balance,
        metadata: data.metadata || {}
      });

      await transaction.save({ session });

      await session.commitTransaction();

      logger.info('wallet credited successfully', {
        walletId: wallet._id,
        userId: wallet.user_id,
        amount: data.amount,
        transactionId: transaction._id,
        balanceBefore,
        balanceAfter: wallet.balance
      });

      return { wallet, transaction };
    } catch (error: any) {
      await session.abortTransaction();
      logger.error('error crediting wallet:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async debitWallet(data: {
    walletId: string;
    amount: number;
    description?: string;
    transactionType?: TransactionType;
    externalRef?: string;
    metadata?: any;
  }): Promise<{ wallet: IWallet; transaction: ITransaction }> {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();

      const wallet = await Wallet.findOne({
        _id: data.walletId,
        status: WalletStatus.ACTIVE
      }).session(session);

      if (!wallet) {
        throw new Error('wallet not found or inactive');
      }

      if (!(wallet as any).canDebit(data.amount)) {
        throw new Error('insufficient balance or wallet cannot be debited');
      }

      const balanceBefore = wallet.balance;
      
      // update wallet balance
      (wallet as any).updateBalance(data.amount, 'debit');
      await wallet.save({ session });

      // create transaction record
      const transaction = new Transaction({
        transaction_ref: CryptoUtils.generateTransactionRef('TXN'),
        user_id: wallet.user_id,
        wallet_id: wallet._id,
        type: data.transactionType || TransactionType.WITHDRAWAL,
        status: TransactionStatus.COMPLETED,
        amount: data.amount,
        currency: wallet.currency,
        description: data.description || 'wallet debit',
        external_reference: data.externalRef,
        balance_before: balanceBefore,
        balance_after: wallet.balance,
        metadata: data.metadata || {}
      });

      await transaction.save({ session });

      await session.commitTransaction();

      logger.info('wallet debited successfully', {
        walletId: wallet._id,
        userId: wallet.user_id,
        amount: data.amount,
        transactionId: transaction._id,
        balanceBefore,
        balanceAfter: wallet.balance
      });

      return { wallet, transaction };
    } catch (error: any) {
      await session.abortTransaction();
      logger.error('error debiting wallet:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async transferBetweenWallets(data: {
    fromWalletId: string;
    toWalletId: string;
    amount: number;
    description?: string;
    metadata?: any;
  }): Promise<{ fromTransaction: ITransaction; toTransaction: ITransaction }> {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();

      const [fromWallet, toWallet] = await Promise.all([
        Wallet.findOne({ _id: data.fromWalletId, status: WalletStatus.ACTIVE }).session(session),
        Wallet.findOne({ _id: data.toWalletId, status: WalletStatus.ACTIVE }).session(session)
      ]);

      if (!fromWallet || !toWallet) {
        throw new Error('one or both wallets not found');
      }

      if (fromWallet.currency !== toWallet.currency) {
        throw new Error('currency mismatch between wallets');
      }

      if (!(fromWallet as any).canDebit(data.amount)) {
        throw new Error('insufficient balance in source wallet');
      }

      if (!(toWallet as any).canCredit()) {
        throw new Error('destination wallet cannot be credited');
      }

      const fromBalanceBefore = fromWallet.balance;
      const toBalanceBefore = toWallet.balance;

      // update balances
      (fromWallet as any).updateBalance(data.amount, 'debit');
      (toWallet as any).updateBalance(data.amount, 'credit');

      await Promise.all([
        fromWallet.save({ session }),
        toWallet.save({ session })
      ]);

      const transferRef = CryptoUtils.generateTransactionRef('TRANSFER');

      // create debit transaction
      const fromTransaction = new Transaction({
        transaction_ref: `${transferRef}_OUT`,
        user_id: fromWallet.user_id,
        wallet_id: fromWallet._id,
        type: TransactionType.TRANSFER,
        status: TransactionStatus.COMPLETED,
        amount: data.amount,
        currency: fromWallet.currency,
        description: data.description || 'wallet transfer',
        recipient_id: toWallet.user_id,
        recipient_wallet_id: toWallet._id,
        balance_before: fromBalanceBefore,
        balance_after: fromWallet.balance,
        metadata: data.metadata || {}
      });

      // create credit transaction
      const toTransaction = new Transaction({
        transaction_ref: `${transferRef}_IN`,
        user_id: toWallet.user_id,
        wallet_id: toWallet._id,
        type: TransactionType.TRANSFER,
        status: TransactionStatus.COMPLETED,
        amount: data.amount,
        currency: toWallet.currency,
        description: data.description || 'wallet transfer',
        recipient_id: fromWallet.user_id,
        recipient_wallet_id: fromWallet._id,
        balance_before: toBalanceBefore,
        balance_after: toWallet.balance,
        metadata: data.metadata || {}
      });

      await Promise.all([
        fromTransaction.save({ session }),
        toTransaction.save({ session })
      ]);

      await session.commitTransaction();

      logger.info('wallet transfer completed', {
        fromWalletId: fromWallet._id,
        toWalletId: toWallet._id,
        amount: data.amount,
        currency: fromWallet.currency,
        fromTransactionId: fromTransaction._id,
        toTransactionId: toTransaction._id
      });

      return { fromTransaction, toTransaction };
    } catch (error: any) {
      await session.abortTransaction();
      logger.error('error transferring between wallets:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async getWalletBalance(walletId: string): Promise<{
    balance: number;
    available_balance: number;
    pending_balance: number;
    currency: Currency;
  } | null> {
    try {
      const wallet = await Wallet.findOne({
        _id: walletId,
        status: { $ne: WalletStatus.CLOSED }
      }).select('balance available_balance pending_balance currency');

      if (!wallet) {
        return null;
      }

      return {
        balance: wallet.balance,
        available_balance: wallet.available_balance,
        pending_balance: wallet.pending_balance,
        currency: wallet.currency
      };
    } catch (error: any) {
      logger.error('error getting wallet balance:', error);
      return null;
    }
  }

  static async freezeWallet(walletId: string, reason?: string): Promise<boolean> {
    try {
      const wallet = await Wallet.findOneAndUpdate(
        { _id: walletId },
        { $set: { status: WalletStatus.FROZEN } },
        { new: true }
      );

      if (wallet) {
        logger.info('wallet frozen', {
          walletId,
          userId: wallet.user_id,
          reason
        });
        return true;
      }

      return false;
    } catch (error: any) {
      logger.error('error freezing wallet:', error);
      return false;
    }
  }

  static async unfreezeWallet(walletId: string): Promise<boolean> {
    try {
      const wallet = await Wallet.findOneAndUpdate(
        { _id: walletId, status: WalletStatus.FROZEN },
        { $set: { status: WalletStatus.ACTIVE } },
        { new: true }
      );

      if (wallet) {
        logger.info('wallet unfrozen', {
          walletId,
          userId: wallet.user_id
        });
        return true;
      }

      return false;
    } catch (error: any) {
      logger.error('error unfreezing wallet:', error);
      return false;
    }
  }
}
