---
type: "manual"
---

# Human-Like Coding Style Rule

When writing code, follow these guidelines to make it sound like it was written by a human developer (Indian/Nigerian style):

## Naming Conventions:
- Use simple, practical names: `getUserData()` not `retrieveUserInformationFromDatabase()`
- Mix of abbreviations: `getDbConnection()`, `validateUserInput()`, `checkAuth()`
- Sometimes use obvious names: `user`, `data`, `result`, `response`, `req`, `res`
- Occasional typos in comments (but not in code): `// chekc if user exists`
- Use common dev shortcuts: `ctx` for context, `db` for database, `auth` for authentication
- the json resposne and json variables should be human like also not too perfect just like a human would  example borroe ideas from {
    "data": {
        "servicesForOwner": [
            {
                "id": "srv-d2k880n5r7bs73ei98eg",
                "env": {
                    "id": "node",
                    "name": "Node",
                },
                "name": "<PERSON><PERSON><PERSON>ey",
                "userFacingType": "Web Service",
                "userFacingTypeSlug": "web",
                "updatedAt": "2025-08-22T15:22:38.345205Z",
                "region": {
                    "id": "oregon",
                },
                "suspenders": [],
                "lastDeployedAt": "2025-08-22T15:22:38.002639Z",
                "environment": null,
                "iacExecutionSource": null,
                "permissions": {
                    "resumeServer": {
                        "permissionLevel": "ALLOWED",
                    },
                    "suspendServer": {
                        "permissionLevel": "ALLOWED",
                    },
                },
            }
        ]
    }
}

## Folder structure
- controllers should be like user.controller.ts , models should be user.model.ts , services sould be user.service.ts and so on and so forth
- before creating a file make sure existing codes do not exists so we dont have duplicates files
-  the controller should be separated like  export const getCurrentUser = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  try {
    const user = await userService.getUserById(request.user.id);

    if (!user) {
      return reply.status(404).send({
        success: false,
        message: 'User not found'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'User profile retrieved successfully',
      data: {
        id: user._id?.toString(),
        email: user.email,
        phone: user.phone,
        username: user.username,
        first_name: user.first_name,
        last_name: user.last_name,
        profile_picture: user.profile_picture,
        bio: user.bio,
        address: user.address,
        role: user.role,
        is_verified: user.is_verified,
        kyc_status: user.kyc_status,
        wallet_balance: user.wallet_balance,
        agent_info: user.agent_info,
        created_at: user.created_at
      }
    });
  } catch (error: any) {
    logger.error('Error fetching user profile:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to fetch user profile'
    });
  }
};   

routes should be like  export async function userRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  fastify.get('/me', { preHandler: authenticateRequest }, getCurrentUser as any);
  fastify.put('/profile', { preHandler: authenticateRequest }, updateProfile as any);
  fastify.put('/password', { preHandler: authenticateRequest }, updatePassword as any);
  fastify.post('/profile-picture', { preHandler: authenticateRequest }, uploadProfilePicture as any);
  fastify.delete('/account', { preHandler: authenticateRequest }, deleteAccount as any);
  fastify.get('/search', { preHandler: authenticateRequest }, searchUsers as any);

  fastify.get('/health', async () => {
    return {
      success: true,
      message: 'User service is running',
      data: { status: 'ok' }
    };
  });
}
   and services also  services can be in class and export everything through class

## Function/Variable Style:
- Keep names short and direct: `saveUser()`, `deleteRecord()`, `updateStatus()`
- Use practical patterns: `isValid`, `hasPermission`, `canAccess`
- Mix camelCase with occasional inconsistencies in comments
- Use common abbreviations: `config`, `utils`, `helpers`, `middleware`

## Comments:
- Minimal comments, only when necessary
- Simple language: `// get user from db`, `// send response`, `// check if valid`
- Occasional informal tone: `// this should work`, `// fix this later`
- No over-explaining obvious code
- Sometimes missing periods or capitals in comments

## Code Structure:
- Practical variable names: `userData`, `dbResult`, `apiResponse`
- Use common patterns developers actually use
- Keep it simple and functional
- Avoid overly descriptive enterprise-style naming

## Examples:
```javascript
// Good (human-like):
const user = await getUserById(id);
if (!user) {
  return res.status(404).send({ error: 'user not found' });
}

// Avoid (AI-like):
const authenticatedUserEntity = await retrieveUserEntityByUniqueIdentifier(userId);
if (!authenticatedUserEntity) {
  return response.status(404).send({ 
    success: false, 
    message: 'The requested user entity could not be located in the database' 
  });
}
```

## Production-Grade Requirements:
- **NO PLACEHOLDER DATA**: Never write demo/test data unless explicitly requested - all code must be production-ready
- **Security First**: Always implement proper authentication, authorization, input validation, and data sanitization
- **Fault Tolerance**: Include proper error handling, circuit breakers, timeouts, and graceful degradation
- **Exponential Backoff**: Implement retry logic with exponential backoff for external API calls and database operations
- **Scalability**: Write code that can handle billions of users - consider database indexing, caching, rate limiting
- **Performance**: Optimize for speed and memory usage - use connection pooling, lazy loading, efficient algorithms
- **Monitoring**: Include proper logging, metrics, and health checks for observability
- **Data Integrity**: Implement transactions, data validation, and consistency checks
- **High Availability**: Design for 99.99% uptime with redundancy and failover mechanisms
- **Google-Level Standards**: Think like a senior engineer at Google/Meta/OpenAI etc - consider edge cases, race conditions, and system limits

## Security Implementation:
- Always validate and sanitize all inputs
- Use parameterized queries to prevent SQL injection
- Implement proper CORS, CSRF, and XSS protection
- Use secure headers and HTTPS everywhere
- Implement rate limiting and DDoS protection
- Hash passwords with bcrypt/argon2, never store plain text
- Use JWT tokens with proper expiration and refresh logic
- Implement proper session management and logout
- Log security events for monitoring and alerting

## Error Handling & Resilience:
- Wrap all async operations in try-catch blocks
- Implement circuit breakers for external service calls
- Use exponential backoff with jitter for retries
- Set proper timeouts for all network operations
- Gracefully handle partial failures and degraded states
- Implement health checks and readiness probes
- Use dead letter queues for failed message processing
- Implement proper cleanup and resource management

## Performance & Scalability:
- Use database connection pooling with proper limits
- Implement caching strategies (Redis, in-memory, CDN)
- Use database indexes for all query patterns
- Implement pagination for large data sets
- Use lazy loading and streaming for large responses
- Optimize database queries and avoid N+1 problems
- Implement proper load balancing and horizontal scaling
- Use async/await patterns for non-blocking operations
- Monitor and optimize memory usage and garbage collection

## Data Management:
- Use database transactions for data consistency
- Implement proper data validation at all layers
- Use foreign key constraints and referential integrity
- Implement soft deletes for audit trails
- Use proper data types and constraints
- Implement data encryption for sensitive information
- Use proper backup and disaster recovery strategies
- Implement data retention and archival policies

## Monitoring & Observability:
- Log all important events with proper log levels
- Include correlation IDs for request tracing
- Implement metrics collection (response times, error rates)
- Use structured logging (JSON format)
- Implement alerting for critical errors and thresholds
- Monitor resource usage (CPU, memory, disk, network)
- Implement distributed tracing for microservices
- Use proper log rotation and retention policies

## Real Developer Patterns:
- Use common variable names like `i`, `j` for loops
- Sometimes use single letter variables: `e` for error, `r` for result
- Use practical shortcuts: `btn` for button, `img` for image, `txt` for text
- Mix of full words and abbreviations naturally
- Use obvious patterns: `getData()`, `setData()`, `checkStatus()`
- Sometimes inconsistent spacing in objects (like real devs do)
- Use common patterns: `try/catch`, `if/else`, `for/while` without over-engineering

## Avoid AI-Like Patterns:
- Don't use overly descriptive names like `userAuthenticationValidationService`
- Avoid perfect documentation-style comments
- Don't use enterprise buzzwords unnecessarily
- Avoid overly structured or "perfect" code organization
- Don't use verbose error messages with perfect grammar
- Avoid overly consistent naming (real devs are slightly inconsistent)

Keep code clean, functional, and like a real developer wrote it - not a documentation generator. Every line of code should be production-ready and capable of handling enterprise-scale traffic and data with the reliability standards of top-tier tech companies.
