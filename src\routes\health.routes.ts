import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { db } from '../config/database';
import { redis } from '../config/redis';
import { logger } from '../config/logger';

export async function healthRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  // Basic health check
  fastify.get('/', async (_request, _reply) => {
    return {
      success: true,
      message: 'Service is healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development'
    };
  });

  // Detailed health check with dependencies
  fastify.get('/detailed', async (_request, reply) => {
    try {
      const startTime = Date.now();
      
      // Check database health
      const dbHealth = await db.healthCheck();
      
      // Check Redis health
      const redisHealth = await redis.healthCheck();
      
      // Check memory usage
      const memoryUsage = process.memoryUsage();
      const memoryUsageMB = {
        rss: Math.round(memoryUsage.rss / 1024 / 1024),
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
        external: Math.round(memoryUsage.external / 1024 / 1024)
      };

      const responseTime = Date.now() - startTime;
      const isHealthy = dbHealth.status === 'healthy' && redisHealth.status === 'healthy';

      const healthData = {
        success: true,
        status: isHealthy ? 'healthy' : 'degraded',
        timestamp: new Date().toISOString(),
        responseTime: `${responseTime}ms`,
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development',
        version: process.env.npm_package_version || '1.0.0',
        dependencies: {
          database: dbHealth,
          redis: redisHealth
        },
        system: {
          memory: memoryUsageMB,
          nodeVersion: process.version,
          platform: process.platform,
          arch: process.arch
        }
      };

      // Set appropriate status code
      const statusCode = isHealthy ? 200 : 503;
      reply.status(statusCode);

      return healthData;
    } catch (error) {
      logger.error('Health check error:', error);
      
      reply.status(503);
      return {
        success: false,
        status: 'unhealthy',
        message: 'Health check failed',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Readiness probe (for Kubernetes)
  fastify.get('/ready', async (_request, reply) => {
    try {
      const dbConnected = db.getConnectionStatus();
      const redisConnected = redis.isRedisConnected();
      
      if (dbConnected && redisConnected) {
        return {
          success: true,
          status: 'ready',
          timestamp: new Date().toISOString()
        };
      } else {
        reply.status(503);
        return {
          success: false,
          status: 'not ready',
          timestamp: new Date().toISOString(),
          details: {
            database: dbConnected ? 'connected' : 'disconnected',
            redis: redisConnected ? 'connected' : 'disconnected'
          }
        };
      }
    } catch (error) {
      logger.error('Readiness check error:', error);
      
      reply.status(503);
      return {
        success: false,
        status: 'not ready',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Liveness probe (for Kubernetes)
  fastify.get('/live', async (_request, _reply) => {
    return {
      success: true,
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    };
  });

  // Database-specific health check
  fastify.get('/database', async (_request, reply) => {
    try {
      const dbHealth = await db.healthCheck();
      const statusCode = dbHealth.status === 'healthy' ? 200 : 503;
      
      reply.status(statusCode);
      return {
        success: dbHealth.status === 'healthy',
        ...dbHealth,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Database health check error:', error);
      
      reply.status(503);
      return {
        success: false,
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });

  // Redis-specific health check
  fastify.get('/redis', async (_request, reply) => {
    try {
      const redisHealth = await redis.healthCheck();
      const statusCode = redisHealth.status === 'healthy' ? 200 : 503;
      
      reply.status(statusCode);
      return {
        success: redisHealth.status === 'healthy',
        ...redisHealth,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Redis health check error:', error);
      
      reply.status(503);
      return {
        success: false,
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });
}
