import mongoose, { <PERSON>hem<PERSON>, Document } from 'mongoose';
import { KycStatus, AccountStatus, Currency } from '../types';
import { CryptoUtils } from '../utils/crypto';

export interface IMerchant extends Document {
  _id: mongoose.Types.ObjectId;
  user_id: mongoose.Types.ObjectId;
  business_name: string;
  business_type: string;
  business_registration_number: string;
  tax_id: string;
  business_address: {
    street: string;
    city: string;
    state: string;
    country: string;
    postal_code: string;
  };
  contact_info: {
    phone: string;
    email: string;
    website?: string;
  };
  kyc_status: KycStatus;
  account_status: AccountStatus;
  verification_documents: {
    business_license?: string;
    tax_certificate?: string;
    bank_statement?: string;
    id_document?: string;
  };
  payment_settings: {
    accepted_currencies: Currency[];
    settlement_currency: Currency;
    settlement_schedule: 'daily' | 'weekly' | 'monthly';
    auto_settlement: boolean;
    minimum_settlement_amount: number;
  };
  fees: {
    transaction_fee_percentage: number;
    fixed_fee: number;
    settlement_fee: number;
    chargeback_fee: number;
  };
  limits: {
    daily_transaction_limit: number;
    monthly_transaction_limit: number;
    single_transaction_limit: number;
  };
  api_credentials: {
    api_key: string;
    secret_key: string;
    webhook_url?: string;
    webhook_secret?: string;
  };
  qr_code: {
    static_qr: string;
    dynamic_qr_enabled: boolean;
  };
  ussd_code?: string;
  statistics: {
    total_transactions: number;
    total_volume: number;
    successful_transactions: number;
    failed_transactions: number;
    last_transaction_date?: Date;
  };
  created_at: Date;
  updated_at: Date;
}

const merchantSchema = new Schema<IMerchant>({
  user_id: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true,
    index: true
  },
  business_name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  business_type: {
    type: String,
    required: true,
    enum: ['retail', 'restaurant', 'service', 'ecommerce', 'marketplace', 'other']
  },
  business_registration_number: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  tax_id: {
    type: String,
    required: true,
    unique: true
  },
  business_address: {
    street: { type: String, required: true },
    city: { type: String, required: true },
    state: { type: String, required: true },
    country: { type: String, required: true },
    postal_code: { type: String, required: true }
  },
  contact_info: {
    phone: { type: String, required: true },
    email: { type: String, required: true },
    website: { type: String, default: '' }
  },
  kyc_status: {
    type: String,
    enum: Object.values(KycStatus),
    default: KycStatus.PENDING,
    index: true
  },
  account_status: {
    type: String,
    enum: Object.values(AccountStatus),
    default: AccountStatus.INACTIVE,
    index: true
  },
  verification_documents: {
    business_license: { type: String, default: '' },
    tax_certificate: { type: String, default: '' },
    bank_statement: { type: String, default: '' },
    id_document: { type: String, default: '' }
  },
  payment_settings: {
    accepted_currencies: [{
      type: String,
      enum: Object.values(Currency)
    }],
    settlement_currency: {
      type: String,
      enum: Object.values(Currency),
      default: Currency.USD
    },
    settlement_schedule: {
      type: String,
      enum: ['daily', 'weekly', 'monthly'],
      default: 'daily'
    },
    auto_settlement: { type: Boolean, default: true },
    minimum_settlement_amount: { type: Number, default: 100 }
  },
  fees: {
    transaction_fee_percentage: { type: Number, default: 0.025 }, // 2.5%
    fixed_fee: { type: Number, default: 0.30 },
    settlement_fee: { type: Number, default: 0 },
    chargeback_fee: { type: Number, default: 15 }
  },
  limits: {
    daily_transaction_limit: { type: Number, default: 50000 },
    monthly_transaction_limit: { type: Number, default: 1000000 },
    single_transaction_limit: { type: Number, default: 10000 }
  },
  api_credentials: {
    api_key: { type: String, required: true, unique: true },
    secret_key: { type: String, required: true },
    webhook_url: { type: String, default: '' },
    webhook_secret: { type: String, default: '' }
  },
  qr_code: {
    static_qr: { type: String, required: true },
    dynamic_qr_enabled: { type: Boolean, default: true }
  },
  ussd_code: {
    type: String,
    unique: true,
    sparse: true
  },
  statistics: {
    total_transactions: { type: Number, default: 0 },
    total_volume: { type: Number, default: 0 },
    successful_transactions: { type: Number, default: 0 },
    failed_transactions: { type: Number, default: 0 },
    last_transaction_date: { type: Date, default: null }
  },
  created_at: {
    type: Date,
    default: Date.now,
    index: true
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  toJSON: {
    transform: function(_doc: any, ret: any) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      // hide sensitive data
      delete ret.api_credentials.secret_key;
      delete ret.api_credentials.webhook_secret;
      return ret;
    }
  }
});

// indexes
merchantSchema.index({ business_registration_number: 1 });
merchantSchema.index({ kyc_status: 1, account_status: 1 });
merchantSchema.index({ 'api_credentials.api_key': 1 });
merchantSchema.index({ created_at: -1 });

// pre-save middleware
merchantSchema.pre('save', function(next) {
  this.updated_at = new Date();
  next();
});

// virtual for success rate
merchantSchema.virtual('success_rate').get(function() {
  if (this.statistics.total_transactions === 0) return 0;
  return (this.statistics.successful_transactions / this.statistics.total_transactions) * 100;
});

// methods
merchantSchema.methods.updateStatistics = function(amount: number, success: boolean) {
  this.statistics.total_transactions += 1;
  this.statistics.total_volume += amount;
  
  if (success) {
    this.statistics.successful_transactions += 1;
  } else {
    this.statistics.failed_transactions += 1;
  }
  
  this.statistics.last_transaction_date = new Date();
};

merchantSchema.methods.canProcessTransaction = function(amount: number): boolean {
  return (
    this.account_status === AccountStatus.ACTIVE &&
    this.kyc_status === KycStatus.APPROVED &&
    amount <= this.limits.single_transaction_limit
  );
};

merchantSchema.methods.calculateFee = function(amount: number): number {
  const percentageFee = amount * this.fees.transaction_fee_percentage;
  return percentageFee + this.fees.fixed_fee;
};

merchantSchema.methods.isWithinDailyLimit = async function(amount: number): Promise<boolean> {
  try {
    // check if amount exceeds daily limit
    if (amount > this.limits.daily_transaction_limit) {
      return false;
    }

    // get today's date range
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

    // calculate today's transaction volume
    const Transaction = mongoose.model('Transaction');
    const todayVolume = await Transaction.aggregate([
      {
        $match: {
          user_id: this.user_id,
          status: 'completed',
          created_at: {
            $gte: startOfDay,
            $lt: endOfDay
          }
        }
      },
      {
        $group: {
          _id: null,
          totalVolume: { $sum: '$amount' }
        }
      }
    ]);

    const currentDailyVolume = todayVolume.length > 0 ? todayVolume[0].totalVolume : 0;

    // check if adding this amount would exceed daily limit
    return (currentDailyVolume + amount) <= this.limits.daily_transaction_limit;
  } catch (error) {
    // if error occurs, be conservative and reject
    console.error('Error checking daily limit:', error);
    return false;
  }
};

merchantSchema.methods.generateQRCode = function(amount?: number, currency?: Currency, description?: string): string {
  // generate QR code data for payment
  const qrData = {
    merchantId: this._id.toString(),
    businessName: this.business_name,
    amount: amount || null,
    currency: currency || this.payment_settings.settlement_currency,
    description: description || `Payment to ${this.business_name}`,
    timestamp: Date.now(),
    // add a hash for verification
    hash: CryptoUtils.generateHash(`${this._id}:${amount || 0}:${Date.now()}`)
  };

  // encode as base64 for QR code
  const qrString = Buffer.from(JSON.stringify(qrData)).toString('base64');

  // return QR code identifier that can be used to generate actual QR image
  return `AETRUST:${qrString}`;
};

// static methods
merchantSchema.statics.findByApiKey = function(apiKey: string) {
  return this.findOne({ 'api_credentials.api_key': apiKey });
};

merchantSchema.statics.findActiveByUserId = function(userId: string) {
  return this.findOne({
    user_id: userId,
    account_status: AccountStatus.ACTIVE
  });
};

export const Merchant = mongoose.model<IMerchant>('Merchant', merchantSchema);
