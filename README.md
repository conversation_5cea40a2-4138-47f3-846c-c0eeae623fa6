# AeTrust Backend API

A production-grade TypeScript backend for the AeTrust fintech platform, built with Fastify, MongoDB, and Redis.

## Features

- 🚀 **High Performance**: Built with Fastify for maximum speed
- 🔒 **Security First**: JWT authentication, 2FA, rate limiting, and comprehensive security middleware
- 📊 **Scalable Architecture**: Microservices-ready with proper separation of concerns
- 💾 **Database**: MongoDB with connection pooling and health monitoring
- ⚡ **Caching**: Redis for session management and performance optimization
- 📝 **Comprehensive Logging**: Structured logging with Winston and daily rotation
- 🧪 **Testing**: Jest setup with coverage reporting
- 🔍 **Code Quality**: ESLint, Prettier, and TypeScript strict mode
- 📈 **Monitoring**: Health checks and system monitoring
- 🐳 **Production Ready**: Docker support and environment configuration

## Tech Stack

- **Runtime**: Node.js 18+
- **Framework**: Fastify 4.x
- **Language**: TypeScript 5.x
- **Database**: MongoDB with Mongoose
- **Cache**: Redis
- **Authentication**: JWT with refresh tokens
- **Validation**: Joi
- **Logging**: Winston
- **Testing**: Jest + Supertest
- **Code Quality**: ESLint + Prettier

## Quick Start

### Prerequisites

- Node.js 18+ and npm 8+
- MongoDB 5.0+
- Redis 6.0+

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd aetrust-backend
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Start development server:
```bash
npm run dev
```

### Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm test` - Run tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run format` - Format code with Prettier

## Project Structure

```
src/
├── config/          # Configuration files
│   ├── database.ts  # MongoDB connection
│   ├── redis.ts     # Redis connection
│   ├── logger.ts    # Logging configuration
│   └── index.ts     # Main config
├── controllers/     # Route controllers
├── models/          # Database models
├── services/        # Business logic
├── middleware/      # Custom middleware
├── routes/          # Route definitions
├── utils/           # Utility functions
├── types/           # TypeScript type definitions
└── server.ts        # Main server file
```

## API Documentation

### Health Endpoints

- `GET /api/v1/health` - Basic health check
- `GET /api/v1/health/detailed` - Detailed health with dependencies
- `GET /api/v1/health/ready` - Readiness probe
- `GET /api/v1/health/live` - Liveness probe

### Authentication

- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh token
- `POST /api/v1/auth/logout` - User logout

## Environment Variables

Key environment variables (see `.env.example` for complete list):

```env
NODE_ENV=development
PORT=3000
MONGODB_URI=mongodb://localhost:27017/aetrust
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-jwt-secret
JWT_REFRESH_SECRET=your-refresh-secret
```

## Security Features

- **Authentication**: JWT with refresh tokens
- **Authorization**: Role-based access control (RBAC)
- **Rate Limiting**: Configurable rate limits per endpoint
- **Input Validation**: Comprehensive validation with Joi
- **Security Headers**: Helmet.js for security headers
- **CORS**: Configurable CORS policies
- **Encryption**: AES-256-GCM for sensitive data
- **Password Hashing**: bcrypt with configurable salt rounds
- **SQL Injection Prevention**: Parameterized queries
- **XSS Protection**: Input sanitization

## Monitoring & Logging

- **Structured Logging**: JSON format with correlation IDs
- **Log Rotation**: Daily rotation with configurable retention
- **Health Checks**: Comprehensive health monitoring
- **Performance Metrics**: Request/response time tracking
- **Error Tracking**: Detailed error logging and reporting

## Testing

Run the test suite:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## Deployment

### Production Build

```bash
npm run build
npm start
```

### Docker (Coming Soon)

```bash
docker build -t aetrust-backend .
docker run -p 3000:3000 aetrust-backend
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

For support and questions, please contact the development team.
