import mongoose, { Schema, Document } from 'mongoose';
import { TransactionType, TransactionStatus, Currency, PaymentMethod } from '../types';

export interface ITransaction extends Document {
  _id: mongoose.Types.ObjectId;
  transaction_ref: string;
  user_id: mongoose.Types.ObjectId;
  wallet_id: mongoose.Types.ObjectId;
  type: TransactionType;
  status: TransactionStatus;
  amount: number;
  fee: number;
  currency: Currency;
  description?: string;
  payment_method?: PaymentMethod;
  
  // for transfers
  recipient_id?: mongoose.Types.ObjectId;
  recipient_wallet_id?: mongoose.Types.ObjectId;
  
  // for external transactions
  external_reference?: string;
  external_provider?: string;
  
  // balances before and after
  balance_before: number;
  balance_after: number;
  
  // metadata
  metadata: {
    ip_address?: string;
    user_agent?: string;
    device_id?: string;
    location?: {
      country?: string;
      city?: string;
      coordinates?: [number, number];
    };
    exchange_rate?: number;
    original_amount?: number;
    original_currency?: string;
  };
  
  // processing info
  processing: {
    initiated_at: Date;
    completed_at?: Date;
    failed_at?: Date;
    retry_count: number;
    last_retry_at?: Date;
    failure_reason?: string;
  };
  
  created_at: Date;
  updated_at: Date;
}

const transactionSchema = new Schema<ITransaction>({
  transaction_ref: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  user_id: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  wallet_id: {
    type: Schema.Types.ObjectId,
    ref: 'Wallet',
    required: true,
    index: true
  },
  type: {
    type: String,
    enum: Object.values(TransactionType),
    required: true,
    index: true
  },
  status: {
    type: String,
    enum: Object.values(TransactionStatus),
    default: TransactionStatus.PENDING,
    index: true
  },
  amount: {
    type: Number,
    required: true,
    min: 0,
    set: (v: number) => Math.round(v * 100) / 100
  },
  fee: {
    type: Number,
    default: 0,
    min: 0,
    set: (v: number) => Math.round(v * 100) / 100
  },
  currency: {
    type: String,
    enum: Object.values(Currency),
    required: true,
    index: true
  },
  description: {
    type: String,
    maxlength: 500,
    trim: true
  },
  payment_method: {
    type: String,
    enum: Object.values(PaymentMethod),
    index: true
  },
  recipient_id: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    index: true
  },
  recipient_wallet_id: {
    type: Schema.Types.ObjectId,
    ref: 'Wallet',
    index: true
  },
  external_reference: {
    type: String,
    index: true
  },
  external_provider: {
    type: String,
    index: true
  },
  balance_before: {
    type: Number,
    required: true,
    set: (v: number) => Math.round(v * 100) / 100
  },
  balance_after: {
    type: Number,
    required: true,
    set: (v: number) => Math.round(v * 100) / 100
  },
  metadata: {
    ip_address: { type: String },
    user_agent: { type: String },
    device_id: { type: String },
    location: {
      country: { type: String },
      city: { type: String },
      coordinates: { type: [Number], index: '2dsphere' }
    },
    exchange_rate: { type: Number },
    original_amount: { type: Number },
    original_currency: { type: String }
  },
  processing: {
    initiated_at: { type: Date, default: Date.now },
    completed_at: { type: Date },
    failed_at: { type: Date },
    retry_count: { type: Number, default: 0 },
    last_retry_at: { type: Date },
    failure_reason: { type: String }
  },
  created_at: {
    type: Date,
    default: Date.now,
    index: true
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  toJSON: {
    transform: function(_doc: any, ret: any) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// compound indexes for performance
transactionSchema.index({ user_id: 1, status: 1, created_at: -1 });
transactionSchema.index({ wallet_id: 1, type: 1, created_at: -1 });
transactionSchema.index({ type: 1, status: 1, created_at: -1 });
transactionSchema.index({ recipient_id: 1, created_at: -1 });
transactionSchema.index({ external_reference: 1, external_provider: 1 });
transactionSchema.index({ 'processing.initiated_at': 1, status: 1 });

// pre-save middleware
transactionSchema.pre('save', function(next) {
  this.updated_at = new Date();
  
  // update processing timestamps based on status
  if (this.status === TransactionStatus.COMPLETED && !this.processing.completed_at) {
    this.processing.completed_at = new Date();
  } else if (this.status === TransactionStatus.FAILED && !this.processing.failed_at) {
    this.processing.failed_at = new Date();
  }
  
  next();
});

// virtual for total amount (amount + fee)
transactionSchema.virtual('total_amount').get(function() {
  return this.amount + this.fee;
});

// virtual for formatted amount
transactionSchema.virtual('formatted_amount').get(function() {
  return `${this.amount.toFixed(2)} ${this.currency}`;
});

// virtual for processing duration
transactionSchema.virtual('processing_duration').get(function() {
  const endTime = this.processing.completed_at || this.processing.failed_at || new Date();
  return endTime.getTime() - this.processing.initiated_at.getTime();
});

// methods
transactionSchema.methods.isDebitTransaction = function(): boolean {
  return [
    TransactionType.WITHDRAWAL,
    TransactionType.TRANSFER,
    TransactionType.PAYMENT,
    TransactionType.FEE
  ].includes(this.type);
};

transactionSchema.methods.isCreditTransaction = function(): boolean {
  return [
    TransactionType.DEPOSIT,
    TransactionType.REFUND,
    TransactionType.COMMISSION
  ].includes(this.type);
};

transactionSchema.methods.canRetry = function(): boolean {
  return this.status === TransactionStatus.FAILED && this.processing.retry_count < 3;
};

transactionSchema.methods.markAsCompleted = function(): void {
  this.status = TransactionStatus.COMPLETED;
  this.processing.completed_at = new Date();
};

transactionSchema.methods.markAsFailed = function(reason: string): void {
  this.status = TransactionStatus.FAILED;
  this.processing.failed_at = new Date();
  this.processing.failure_reason = reason;
};

transactionSchema.methods.incrementRetry = function(): void {
  this.processing.retry_count += 1;
  this.processing.last_retry_at = new Date();
};

// static methods
transactionSchema.statics.findUserTransactions = function(
  userId: string, 
  options: {
    type?: TransactionType;
    status?: TransactionStatus;
    limit?: number;
    skip?: number;
  } = {}
) {
  const query: any = { user_id: userId };
  
  if (options.type) query.type = options.type;
  if (options.status) query.status = options.status;
  
  return this.find(query)
    .sort({ created_at: -1 })
    .limit(options.limit || 50)
    .skip(options.skip || 0)
    .populate('recipient_id', 'first_name last_name email')
    .populate('wallet_id', 'wallet_type currency');
};

transactionSchema.statics.findPendingTransactions = function() {
  return this.find({
    status: TransactionStatus.PENDING,
    'processing.initiated_at': { $lt: new Date(Date.now() - 5 * 60 * 1000) } // older than 5 minutes
  });
};

transactionSchema.statics.getTransactionStats = function(userId: string, period: 'day' | 'week' | 'month' = 'month') {
  const now = new Date();
  let startDate: Date;
  
  switch (period) {
    case 'day':
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      break;
    case 'week':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case 'month':
    default:
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      break;
  }
  
  return this.aggregate([
    {
      $match: {
        user_id: new mongoose.Types.ObjectId(userId),
        created_at: { $gte: startDate },
        status: TransactionStatus.COMPLETED
      }
    },
    {
      $group: {
        _id: '$type',
        count: { $sum: 1 },
        total_amount: { $sum: '$amount' },
        total_fee: { $sum: '$fee' }
      }
    }
  ]);
};

export const Transaction = mongoose.model<ITransaction>('Transaction', transactionSchema);
