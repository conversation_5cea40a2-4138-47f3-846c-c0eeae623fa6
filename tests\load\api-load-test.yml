# Artillery Load Test Configuration for AeTrust API

config:
  target: 'http://localhost:3000'
  phases:
    # Warm-up phase
    - duration: 60
      arrivalRate: 5
      name: "Warm-up"
    
    # Ramp-up phase
    - duration: 120
      arrivalRate: 5
      rampTo: 50
      name: "Ramp-up"
    
    # Sustained load
    - duration: 300
      arrivalRate: 50
      name: "Sustained load"
    
    # Peak load
    - duration: 120
      arrivalRate: 50
      rampTo: 100
      name: "Peak load"
    
    # Cool-down
    - duration: 60
      arrivalRate: 100
      rampTo: 5
      name: "Cool-down"

  defaults:
    headers:
      Content-Type: 'application/json'
      User-Agent: 'Artillery Load Test'

  variables:
    testEmail:
      - "<EMAIL>"
      - "<EMAIL>"
      - "<EMAIL>"
    testPassword: "TestPassword123!"

scenarios:
  # Health check scenario
  - name: "Health Check"
    weight: 10
    flow:
      - get:
          url: "/health"
          expect:
            - statusCode: 200

  # Authentication flow
  - name: "Authentication Flow"
    weight: 30
    flow:
      # Register user
      - post:
          url: "/api/v1/auth/register"
          json:
            email: "{{ $randomString() }}@loadtest.com"
            password: "{{ testPassword }}"
            first_name: "Load"
            last_name: "Test"
            phone: "+250{{ $randomInt(700000000, 799999999) }}"
          capture:
            - json: "$.data.user.id"
              as: "userId"
            - json: "$.data.tokens.access_token"
              as: "accessToken"

      # Login
      - post:
          url: "/api/v1/auth/login"
          json:
            email: "{{ testEmail }}"
            password: "{{ testPassword }}"
          capture:
            - json: "$.data.tokens.access_token"
              as: "loginToken"

  # User profile operations
  - name: "User Profile Operations"
    weight: 20
    flow:
      # Login first
      - post:
          url: "/api/v1/auth/login"
          json:
            email: "{{ testEmail }}"
            password: "{{ testPassword }}"
          capture:
            - json: "$.data.tokens.access_token"
              as: "accessToken"

      # Get user profile
      - get:
          url: "/api/v1/users/me"
          headers:
            Authorization: "Bearer {{ accessToken }}"
          expect:
            - statusCode: 200

      # Update profile
      - put:
          url: "/api/v1/users/profile"
          headers:
            Authorization: "Bearer {{ accessToken }}"
          json:
            first_name: "Updated"
            last_name: "Name"
            bio: "Load test user"

  # Transfer operations
  - name: "Transfer Operations"
    weight: 25
    flow:
      # Login
      - post:
          url: "/api/v1/auth/login"
          json:
            email: "{{ testEmail }}"
            password: "{{ testPassword }}"
          capture:
            - json: "$.data.tokens.access_token"
              as: "accessToken"

      # Get exchange rates
      - get:
          url: "/api/v1/transfers/exchange-rates"
          headers:
            Authorization: "Bearer {{ accessToken }}"
          qs:
            from: "USD"
            to: "RWF"

      # Get bill providers
      - get:
          url: "/api/v1/transfers/bill-providers"
          headers:
            Authorization: "Bearer {{ accessToken }}"
          qs:
            country: "RW"
            type: "electricity"

      # Get transfer history
      - get:
          url: "/api/v1/transfers/history"
          headers:
            Authorization: "Bearer {{ accessToken }}"
          qs:
            page: 1
            limit: 10

  # Agent operations
  - name: "Agent Operations"
    weight: 10
    flow:
      # Login
      - post:
          url: "/api/v1/auth/login"
          json:
            email: "{{ testEmail }}"
            password: "{{ testPassword }}"
          capture:
            - json: "$.data.tokens.access_token"
              as: "accessToken"

      # Search agents
      - get:
          url: "/api/v1/agents/search"
          headers:
            Authorization: "Bearer {{ accessToken }}"
          qs:
            location: "Kigali"
            service: "cash_in"

  # Admin operations (limited)
  - name: "Admin Operations"
    weight: 5
    flow:
      # System health
      - get:
          url: "/api/v1/admin/health"
          headers:
            Authorization: "Bearer admin_token"
          expect:
            - statusCode: [200, 401, 403]

# Performance thresholds
expect:
  - statusCode: [200, 201, 400, 401, 403, 404]
  - contentType: json

# Metrics to track
metrics:
  - name: "response_time_p95"
    threshold: 2000
  - name: "response_time_p99"
    threshold: 5000
  - name: "error_rate"
    threshold: 5
