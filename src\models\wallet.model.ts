import mongoose, { Schema, Document } from 'mongoose';
import { WalletType, WalletStatus, Currency } from '../types';

export interface IWallet extends Document {
  _id: mongoose.Types.ObjectId;
  user_id: mongoose.Types.ObjectId;
  wallet_type: WalletType;
  currency: Currency;
  balance: number;
  available_balance: number;
  pending_balance: number;
  status: WalletStatus;
  wallet_address?: string;
  is_default: boolean;
  limits: {
    daily_limit: number;
    monthly_limit: number;
    transaction_limit: number;
  };
  metadata: {
    last_transaction_date?: Date;
    total_transactions: number;
    total_credited: number;
    total_debited: number;
  };
  created_at: Date;
  updated_at: Date;
}

const walletSchema = new Schema<IWallet>({
  user_id: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  wallet_type: {
    type: String,
    enum: Object.values(WalletType),
    default: WalletType.MAIN,
    index: true
  },
  currency: {
    type: String,
    enum: Object.values(Currency),
    required: true,
    index: true
  },
  balance: {
    type: Number,
    default: 0,
    min: 0,
    set: (v: number) => Math.round(v * 100) / 100 // round to 2 decimal places
  },
  available_balance: {
    type: Number,
    default: 0,
    min: 0,
    set: (v: number) => Math.round(v * 100) / 100
  },
  pending_balance: {
    type: Number,
    default: 0,
    min: 0,
    set: (v: number) => Math.round(v * 100) / 100
  },
  status: {
    type: String,
    enum: Object.values(WalletStatus),
    default: WalletStatus.ACTIVE,
    index: true
  },
  wallet_address: {
    type: String,
    unique: true,
    sparse: true,
    index: true
  },
  is_default: {
    type: Boolean,
    default: false,
    index: true
  },
  limits: {
    daily_limit: { type: Number, default: 10000 },
    monthly_limit: { type: Number, default: 100000 },
    transaction_limit: { type: Number, default: 5000 }
  },
  metadata: {
    last_transaction_date: { type: Date, default: null },
    total_transactions: { type: Number, default: 0 },
    total_credited: { type: Number, default: 0 },
    total_debited: { type: Number, default: 0 }
  },
  created_at: {
    type: Date,
    default: Date.now,
    index: true
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  toJSON: {
    transform: function(_doc: any, ret: any) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      return ret;
    }
  }
});

// compound indexes for performance
walletSchema.index({ user_id: 1, wallet_type: 1, currency: 1 });
walletSchema.index({ user_id: 1, is_default: 1 });
walletSchema.index({ wallet_address: 1 }, { sparse: true });
walletSchema.index({ status: 1, created_at: -1 });

// pre-save middleware
walletSchema.pre('save', function(next) {
  this.updated_at = new Date();
  
  // ensure available_balance doesn't exceed balance
  if (this.available_balance > this.balance) {
    this.available_balance = this.balance;
  }
  
  // ensure pending_balance + available_balance doesn't exceed balance
  if (this.pending_balance + this.available_balance > this.balance) {
    this.pending_balance = this.balance - this.available_balance;
  }
  
  next();
});

// virtual for formatted balance
walletSchema.virtual('formatted_balance').get(function() {
  return `${this.balance.toFixed(2)} ${this.currency}`;
});

// methods
walletSchema.methods.canDebit = function(amount: number): boolean {
  return this.available_balance >= amount && this.status === WalletStatus.ACTIVE;
};

walletSchema.methods.canCredit = function(): boolean {
  return this.status === WalletStatus.ACTIVE;
};

walletSchema.methods.isWithinDailyLimit = async function(amount: number): Promise<boolean> {
  try {
    // check if amount exceeds daily limit
    if (amount > this.limits.daily_limit) {
      return false;
    }

    // get today's date range
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

    // calculate today's spending (debit transactions)
    const Transaction = mongoose.model('Transaction');
    const todaySpending = await Transaction.aggregate([
      {
        $match: {
          wallet_id: this._id,
          type: { $in: ['p2p_transfer', 'cash_out', 'bill_payment', 'remittance'] },
          status: 'completed',
          created_at: {
            $gte: startOfDay,
            $lt: endOfDay
          }
        }
      },
      {
        $group: {
          _id: null,
          totalSpent: { $sum: '$amount' }
        }
      }
    ]);

    const currentDailySpending = todaySpending.length > 0 ? todaySpending[0].totalSpent : 0;

    // check if adding this amount would exceed daily limit
    return (currentDailySpending + amount) <= this.limits.daily_limit;
  } catch (error) {
    // if error occurs, be conservative and reject
    console.error('Error checking daily spending limit:', error);
    return false;
  }
};

walletSchema.methods.isWithinTransactionLimit = function(amount: number): boolean {
  return amount <= this.limits.transaction_limit;
};

walletSchema.methods.updateBalance = function(amount: number, type: 'credit' | 'debit'): void {
  if (type === 'credit') {
    this.balance += amount;
    this.available_balance += amount;
    this.metadata.total_credited += amount;
  } else {
    this.balance -= amount;
    this.available_balance -= amount;
    this.metadata.total_debited += amount;
  }
  
  this.metadata.total_transactions += 1;
  this.metadata.last_transaction_date = new Date();
};

walletSchema.methods.holdFunds = function(amount: number): boolean {
  if (this.available_balance >= amount) {
    this.available_balance -= amount;
    this.pending_balance += amount;
    return true;
  }
  return false;
};

walletSchema.methods.releaseFunds = function(amount: number): boolean {
  if (this.pending_balance >= amount) {
    this.pending_balance -= amount;
    this.available_balance += amount;
    return true;
  }
  return false;
};

walletSchema.methods.confirmHeldFunds = function(amount: number): boolean {
  if (this.pending_balance >= amount) {
    this.pending_balance -= amount;
    this.balance -= amount;
    this.metadata.total_debited += amount;
    this.metadata.total_transactions += 1;
    this.metadata.last_transaction_date = new Date();
    return true;
  }
  return false;
};

// static methods
walletSchema.statics.findUserWallets = function(userId: string, currency?: Currency) {
  const query: any = { user_id: userId };
  if (currency) query.currency = currency;
  return this.find(query).sort({ is_default: -1, created_at: 1 });
};

walletSchema.statics.findDefaultWallet = function(userId: string, currency: Currency) {
  return this.findOne({ 
    user_id: userId, 
    currency, 
    is_default: true,
    status: WalletStatus.ACTIVE 
  });
};

export const Wallet = mongoose.model<IWallet>('Wallet', walletSchema);
