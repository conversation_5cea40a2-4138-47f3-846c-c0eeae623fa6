import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { AuthMiddleware } from '../middleware/auth.middleware';
import * as adminController from '../controllers/admin.controller';

export async function adminRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  // dashboard and monitoring
  fastify.get('/dashboard', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getDashboardStats as any);
  fastify.get('/health', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getSystemHealth as any);
  
  // audit and compliance
  fastify.get('/audit-logs', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getAuditLogs as any);
  fastify.get('/fraud-alerts', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getFraudAlerts as any);
  
  // reporting
  fastify.get('/reports', { preHandler: AuthMiddleware.authenticateRequest }, adminController.getReports as any);

  fastify.get('/service-health', async () => {
    return {
      success: true,
      message: 'admin service running',
      data: { 
        status: 'ok',
        services: ['dashboard', 'monitoring', 'audit_logs', 'fraud_detection', 'reporting']
      }
    };
  });
}
