import { Transaction, ITransaction } from '../models/transaction.model';
import { TransactionType, TransactionStatus, Currency } from '../types';
import { logger } from '../config/logger';
import mongoose from 'mongoose';

export class TransactionService {
  static async getTransactionById(transactionId: string): Promise<ITransaction | null> {
    try {
      if (!mongoose.Types.ObjectId.isValid(transactionId)) {
        return null;
      }

      const transaction = await Transaction.findById(transactionId)
        .populate('user_id', 'first_name last_name email')
        .populate('recipient_id', 'first_name last_name email')
        .populate('wallet_id', 'wallet_type currency balance');

      return transaction;
    } catch (error: any) {
      logger.error('error getting transaction by id:', error);
      return null;
    }
  }

  static async getTransactionByRef(transactionRef: string): Promise<ITransaction | null> {
    try {
      const transaction = await Transaction.findOne({ transaction_ref: transactionRef })
        .populate('user_id', 'first_name last_name email')
        .populate('recipient_id', 'first_name last_name email')
        .populate('wallet_id', 'wallet_type currency balance');

      return transaction;
    } catch (error: any) {
      logger.error('error getting transaction by ref:', error);
      return null;
    }
  }

  static async getUserTransactions(
    userId: string,
    options: {
      type?: TransactionType;
      status?: TransactionStatus;
      currency?: Currency;
      page?: number;
      limit?: number;
      startDate?: Date;
      endDate?: Date;
    } = {}
  ): Promise<{
    transactions: ITransaction[];
    total: number;
    page: number;
    pages: number;
  }> {
    try {
      const page = options.page || 1;
      const limit = Math.min(options.limit || 20, 100);
      const skip = (page - 1) * limit;

      const filter: any = { user_id: userId };

      if (options.type) filter.type = options.type;
      if (options.status) filter.status = options.status;
      if (options.currency) filter.currency = options.currency;
      
      if (options.startDate || options.endDate) {
        filter.created_at = {};
        if (options.startDate) filter.created_at.$gte = options.startDate;
        if (options.endDate) filter.created_at.$lte = options.endDate;
      }

      const [transactions, total] = await Promise.all([
        Transaction.find(filter)
          .sort({ created_at: -1 })
          .skip(skip)
          .limit(limit)
          .populate('recipient_id', 'first_name last_name email')
          .populate('wallet_id', 'wallet_type currency')
          .lean(),
        Transaction.countDocuments(filter)
      ]);

      return {
        transactions: transactions as ITransaction[],
        total,
        page,
        pages: Math.ceil(total / limit)
      };
    } catch (error: any) {
      logger.error('error getting user transactions:', error);
      throw error;
    }
  }

  static async getTransactionStats(
    userId: string,
    period: 'day' | 'week' | 'month' | 'year' = 'month'
  ): Promise<{
    total_transactions: number;
    total_sent: number;
    total_received: number;
    total_fees: number;
    by_type: any[];
    by_currency: any[];
  }> {
    try {
      const now = new Date();
      let startDate: Date;

      switch (period) {
        case 'day':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'year':
          startDate = new Date(now.getFullYear(), 0, 1);
          break;
        case 'month':
        default:
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
      }

      const [generalStats, typeStats, currencyStats] = await Promise.all([
        Transaction.aggregate([
          {
            $match: {
              user_id: new mongoose.Types.ObjectId(userId),
              created_at: { $gte: startDate },
              status: TransactionStatus.COMPLETED
            }
          },
          {
            $group: {
              _id: null,
              total_transactions: { $sum: 1 },
              total_sent: {
                $sum: {
                  $cond: [
                    { $in: ['$type', [TransactionType.TRANSFER, TransactionType.PAYMENT, TransactionType.WITHDRAWAL]] },
                    '$amount',
                    0
                  ]
                }
              },
              total_received: {
                $sum: {
                  $cond: [
                    { $in: ['$type', [TransactionType.DEPOSIT, TransactionType.REFUND]] },
                    '$amount',
                    0
                  ]
                }
              },
              total_fees: { $sum: '$fee' }
            }
          }
        ]),
        Transaction.aggregate([
          {
            $match: {
              user_id: new mongoose.Types.ObjectId(userId),
              created_at: { $gte: startDate },
              status: TransactionStatus.COMPLETED
            }
          },
          {
            $group: {
              _id: '$type',
              count: { $sum: 1 },
              total_amount: { $sum: '$amount' },
              avg_amount: { $avg: '$amount' }
            }
          }
        ]),
        Transaction.aggregate([
          {
            $match: {
              user_id: new mongoose.Types.ObjectId(userId),
              created_at: { $gte: startDate },
              status: TransactionStatus.COMPLETED
            }
          },
          {
            $group: {
              _id: '$currency',
              count: { $sum: 1 },
              total_amount: { $sum: '$amount' }
            }
          }
        ])
      ]);

      const stats = generalStats[0] || {
        total_transactions: 0,
        total_sent: 0,
        total_received: 0,
        total_fees: 0
      };

      return {
        ...stats,
        by_type: typeStats,
        by_currency: currencyStats
      };
    } catch (error: any) {
      logger.error('error getting transaction stats:', error);
      throw error;
    }
  }

  static async getPendingTransactions(limit: number = 100): Promise<ITransaction[]> {
    try {
      const transactions = await Transaction.find({
        status: TransactionStatus.PENDING,
        'processing.initiated_at': { 
          $lt: new Date(Date.now() - 5 * 60 * 1000) // older than 5 minutes
        }
      })
      .limit(limit)
      .sort({ 'processing.initiated_at': 1 });

      return transactions;
    } catch (error: any) {
      logger.error('error getting pending transactions:', error);
      return [];
    }
  }

  static async updateTransactionStatus(
    transactionId: string,
    status: TransactionStatus,
    reason?: string
  ): Promise<boolean> {
    try {
      const updateData: any = { status };

      if (status === TransactionStatus.COMPLETED) {
        updateData['processing.completed_at'] = new Date();
      } else if (status === TransactionStatus.FAILED) {
        updateData['processing.failed_at'] = new Date();
        if (reason) {
          updateData['processing.failure_reason'] = reason;
        }
      }

      const transaction = await Transaction.findByIdAndUpdate(
        transactionId,
        { $set: updateData },
        { new: true }
      );

      if (transaction) {
        logger.info('transaction status updated', {
          transactionId,
          oldStatus: transaction.status,
          newStatus: status,
          reason
        });
        return true;
      }

      return false;
    } catch (error: any) {
      logger.error('error updating transaction status:', error);
      return false;
    }
  }

  static async retryFailedTransaction(transactionId: string): Promise<boolean> {
    try {
      const transaction = await Transaction.findById(transactionId);

      if (!transaction) {
        throw new Error('transaction not found');
      }

      if (!(transaction as any).canRetry()) {
        throw new Error('transaction cannot be retried');
      }

      (transaction as any).incrementRetry();
      transaction.status = TransactionStatus.PROCESSING;
      
      await transaction.save();

      logger.info('transaction retry initiated', {
        transactionId,
        retryCount: transaction.processing.retry_count
      });

      return true;
    } catch (error: any) {
      logger.error('error retrying transaction:', error);
      return false;
    }
  }

  static async searchTransactions(query: {
    search?: string;
    type?: TransactionType;
    status?: TransactionStatus;
    currency?: Currency;
    userId?: string;
    startDate?: Date;
    endDate?: Date;
    minAmount?: number;
    maxAmount?: number;
    page?: number;
    limit?: number;
  }): Promise<{
    transactions: ITransaction[];
    total: number;
    page: number;
    pages: number;
  }> {
    try {
      const page = query.page || 1;
      const limit = Math.min(query.limit || 20, 100);
      const skip = (page - 1) * limit;

      const filter: any = {};

      if (query.search) {
        filter.$or = [
          { transaction_ref: { $regex: query.search, $options: 'i' } },
          { description: { $regex: query.search, $options: 'i' } },
          { external_reference: { $regex: query.search, $options: 'i' } }
        ];
      }

      if (query.type) filter.type = query.type;
      if (query.status) filter.status = query.status;
      if (query.currency) filter.currency = query.currency;
      if (query.userId) filter.user_id = query.userId;

      if (query.startDate || query.endDate) {
        filter.created_at = {};
        if (query.startDate) filter.created_at.$gte = query.startDate;
        if (query.endDate) filter.created_at.$lte = query.endDate;
      }

      if (query.minAmount || query.maxAmount) {
        filter.amount = {};
        if (query.minAmount) filter.amount.$gte = query.minAmount;
        if (query.maxAmount) filter.amount.$lte = query.maxAmount;
      }

      const [transactions, total] = await Promise.all([
        Transaction.find(filter)
          .sort({ created_at: -1 })
          .skip(skip)
          .limit(limit)
          .populate('user_id', 'first_name last_name email')
          .populate('recipient_id', 'first_name last_name email')
          .lean(),
        Transaction.countDocuments(filter)
      ]);

      return {
        transactions: transactions as ITransaction[],
        total,
        page,
        pages: Math.ceil(total / limit)
      };
    } catch (error: any) {
      logger.error('error searching transactions:', error);
      throw error;
    }
  }
}
