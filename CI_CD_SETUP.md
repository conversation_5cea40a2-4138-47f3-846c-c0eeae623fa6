# AeTrust Backend CI/CD Pipeline Setup Guide

## 🚀 Overview

This guide will help you set up a complete CI/CD pipeline for the AeTrust backend using GitHub Actions, Docker, and automated deployment.

## 📋 Prerequisites

- GitHub repository
- Docker Hub or GitHub Container Registry account
- Server for staging/production deployment
- Domain name (optional)

## 🔧 Quick Setup

### 1. Repository Setup

```bash
# Clone your repository
git clone <your-repo-url>
cd aetrust-backend

# Run setup script
chmod +x scripts/setup.sh
./scripts/setup.sh
```

### 2. GitHub Secrets Configuration

Go to your GitHub repository → Settings → Secrets and Variables → Actions

Add these secrets:

#### **Production Secrets**
```
PRODUCTION_HOST=your-production-server-ip
PRODUCTION_USER=deploy
PRODUCTION_SSH_KEY=your-private-ssh-key
PRODUCTION_PORT=22
PRODUCTION_URL=https://api.yourdomain.com

# Database
MONGODB_URI=***************************************************
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=secure-password

# Redis
REDIS_URL=redis://host:6379
REDIS_PASSWORD=secure-redis-password

# JWT
JWT_SECRET=your-super-secure-jwt-secret
JWT_REFRESH_SECRET=your-super-secure-refresh-secret

# Encryption
ENCRYPTION_KEY=your-32-character-encryption-key
```

#### **Staging Secrets**
```
STAGING_HOST=your-staging-server-ip
STAGING_USER=deploy
STAGING_SSH_KEY=your-private-ssh-key
STAGING_PORT=22
STAGING_URL=https://staging-api.yourdomain.com
```

#### **Notification Secrets**
```
SLACK_WEBHOOK=your-slack-webhook-url
```

### 3. Server Setup

#### **Install Docker on your server:**
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### **Create deployment directory:**
```bash
sudo mkdir -p /opt/aetrust-backend
sudo chown $USER:$USER /opt/aetrust-backend
cd /opt/aetrust-backend

# Copy configuration files
scp -r config/ user@server:/opt/aetrust-backend/
scp docker-compose.prod.yml user@server:/opt/aetrust-backend/
```

#### **Setup SSL certificates:**
```bash
# Using Let's Encrypt (recommended)
sudo apt install certbot
sudo certbot certonly --standalone -d api.yourdomain.com

# Copy certificates
sudo cp /etc/letsencrypt/live/api.yourdomain.com/fullchain.pem /opt/aetrust-backend/ssl/cert.pem
sudo cp /etc/letsencrypt/live/api.yourdomain.com/privkey.pem /opt/aetrust-backend/ssl/key.pem
sudo chown $USER:$USER /opt/aetrust-backend/ssl/*
```

## 🔄 Pipeline Workflow

### **Branch Strategy**
- `main` → Production deployment
- `develop` → Staging deployment
- `feature/*` → Pull request testing only

### **Pipeline Stages**

1. **Code Quality & Security**
   - ESLint checking
   - TypeScript compilation
   - Security vulnerability scanning
   - Code formatting validation

2. **Build & Test**
   - Install dependencies
   - Build application
   - Run unit tests
   - Generate coverage reports

3. **Docker Build**
   - Build multi-platform Docker images
   - Push to GitHub Container Registry
   - Tag with branch and commit SHA

4. **Deploy Staging** (develop branch)
   - Deploy to staging environment
   - Run health checks
   - Execute performance tests

5. **Deploy Production** (main branch)
   - Deploy to production environment
   - Run database migrations
   - Perform comprehensive health checks
   - Send notifications

## 🐳 Docker Deployment

### **Development**
```bash
# Start development environment
docker-compose up -d

# View logs
docker-compose logs -f api

# Stop environment
docker-compose down
```

### **Production**
```bash
# Deploy to production
./scripts/deploy.sh production

# Check status
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f api
```

## 📊 Monitoring Setup

### **Prometheus + Grafana**
```bash
# Access Grafana
http://your-server:3001
# Default: admin/admin123

# Import dashboards
- Node.js Application Metrics
- MongoDB Metrics
- Redis Metrics
- Nginx Metrics
```

### **Log Management**
```bash
# Access Kibana
http://your-server:5601

# View application logs
- Index pattern: aetrust-*
- Time field: @timestamp
```

## 🔒 Security Configuration

### **Environment Variables**
```bash
# Production .env
NODE_ENV=production
PORT=3000
MONGODB_URI=**********************************************
REDIS_URL=redis://redis:6379
JWT_SECRET=your-production-jwt-secret
JWT_REFRESH_SECRET=your-production-refresh-secret
ENCRYPTION_KEY=your-32-char-encryption-key
LOG_LEVEL=info
API_RATE_LIMIT=100
```

### **Firewall Setup**
```bash
# Ubuntu UFW
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw enable
```

## 🧪 Testing

### **Load Testing**
```bash
# Install Artillery
npm install -g artillery

# Run load tests
artillery run tests/load/api-load-test.yml --target https://staging-api.yourdomain.com
```

### **Health Checks**
```bash
# API Health
curl https://api.yourdomain.com/health

# Database Health
curl https://api.yourdomain.com/api/v1/admin/health

# Metrics
curl https://api.yourdomain.com/metrics
```

## 📦 Backup & Recovery

### **Automated Backups**
```bash
# Setup cron job for daily backups
crontab -e

# Add this line for daily backup at 2 AM
0 2 * * * /opt/aetrust-backend/scripts/backup.sh
```

### **Manual Backup**
```bash
# Create backup
./scripts/backup.sh

# Restore from backup
./scripts/restore.sh latest
```

## 🚨 Troubleshooting

### **Common Issues**

1. **Build Failures**
   ```bash
   # Check Node.js version
   node --version  # Should be 18+
   
   # Clear npm cache
   npm cache clean --force
   
   # Reinstall dependencies
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **Docker Issues**
   ```bash
   # Check Docker daemon
   sudo systemctl status docker
   
   # Restart Docker
   sudo systemctl restart docker
   
   # Clean Docker system
   docker system prune -a
   ```

3. **Database Connection**
   ```bash
   # Check MongoDB container
   docker-compose logs mongodb
   
   # Test connection
   docker-compose exec mongodb mongosh
   ```

4. **SSL Certificate Issues**
   ```bash
   # Renew Let's Encrypt certificate
   sudo certbot renew
   
   # Check certificate expiry
   openssl x509 -in ssl/cert.pem -text -noout | grep "Not After"
   ```

## 📈 Performance Optimization

### **Production Tuning**
```bash
# MongoDB
- Enable sharding for large datasets
- Configure replica sets for high availability
- Optimize indexes for query patterns

# Redis
- Configure memory limits
- Enable persistence
- Set up clustering for high availability

# Node.js
- Use PM2 for process management
- Enable cluster mode
- Configure memory limits
```

## 🔄 Deployment Commands

```bash
# Quick deployment
./scripts/deploy.sh production

# Deploy with backup
BACKUP_ENABLED=true ./scripts/deploy.sh production

# Deploy without health checks
HEALTH_CHECK_TIMEOUT=0 ./scripts/deploy.sh production

# Rollback deployment
ROLLBACK_ON_FAILURE=true ./scripts/deploy.sh production
```

## 📞 Support

For issues or questions:
1. Check the logs: `docker-compose logs -f`
2. Review health checks: `curl /health`
3. Check monitoring dashboards
4. Review this documentation

## 🎉 Success!

Your CI/CD pipeline is now set up! Every push to `main` will automatically deploy to production, and pushes to `develop` will deploy to staging.

**Happy deploying! 🚀**
