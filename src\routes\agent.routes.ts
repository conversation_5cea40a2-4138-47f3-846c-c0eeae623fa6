import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { AuthMiddleware } from '../middleware/auth.middleware';
import * as agentController from '../controllers/agent.controller';

export async function agentRoutes(fastify: FastifyInstance, _options: FastifyPluginOptions) {
  // agent registration and profile
  fastify.post('/register', { preHandler: AuthMiddleware.authenticateRequest }, agentController.registerAgent as any);
  fastify.get('/profile', { preHandler: AuthMiddleware.authenticateRequest }, agentController.getAgentProfile as any);
  
  // cash in/out operations
  fastify.post('/cash-in', { preHandler: AuthMiddleware.authenticateRequest }, agentController.performCashIn as any);
  fastify.post('/cash-out', { preHandler: AuthMiddleware.authenticateRequest }, agentController.performCashOut as any);
  
  // public agent lookup
  fastify.get('/search', agentController.searchAgents as any);
  fastify.get('/code/:agentCode', agentController.getAgentByCode as any);

  fastify.get('/health', async () => {
    return {
      success: true,
      message: 'agent service running',
      data: {
        status: 'ok',
        services: ['registration', 'cash_in', 'cash_out', 'agent_lookup']
      }
    };
  });
}
