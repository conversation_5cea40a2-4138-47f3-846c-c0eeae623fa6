import { FastifyRequest, FastifyReply } from 'fastify';
import { securityLogger } from '../config/logger';
import { CryptoUtils } from '../utils/crypto';

export class SecurityMiddleware {
  /**
   * Request ID middleware for tracing
   */
  static async requestId(request: FastifyRequest, reply: FastifyReply): Promise<void> {
    const requestId = CryptoUtils.generateUUID();
    (request as any).requestId = requestId;
    reply.header('X-Request-ID', requestId);
  }

  /**
   * Security headers middleware
   */
  static async securityHeaders(_request: FastifyRequest, reply: FastifyReply): Promise<void> {
    // Remove server information
    reply.removeHeader('X-Powered-By');
    reply.removeHeader('Server');
    
    // Add security headers
    reply.header('X-Content-Type-Options', 'nosniff');
    reply.header('X-Frame-Options', 'DENY');
    reply.header('X-XSS-Protection', '1; mode=block');
    reply.header('Referrer-Policy', 'strict-origin-when-cross-origin');
    reply.header('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
    
    // HSTS for production
    if (process.env.NODE_ENV === 'production') {
      reply.header('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
    }
  }

  /**
   * IP whitelist middleware
   */
  static ipWhitelist(allowedIPs: string[]) {
    return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
      const clientIP = request.ip;
      
      if (!allowedIPs.includes(clientIP)) {
        securityLogger.warn('IP access denied', {
          ip: clientIP,
          url: request.url,
          userAgent: request.headers['user-agent']
        });
        
        return reply.status(403).send({
          success: false,
          message: 'Access denied'
        });
      }
    };
  }

  /**
   * Request size limiter
   */
  static requestSizeLimit(maxSize: number) {
    return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
      const contentLength = request.headers['content-length'];
      
      if (contentLength && parseInt(contentLength) > maxSize) {
        securityLogger.warn('Request size limit exceeded', {
          contentLength,
          maxSize,
          ip: request.ip,
          url: request.url
        });
        
        return reply.status(413).send({
          success: false,
          message: 'Request entity too large'
        });
      }
    };
  }

  /**
   * User agent validation
   */
  static validateUserAgent() {
    return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
      const userAgent = request.headers['user-agent'];
      
      if (!userAgent || userAgent.length < 10) {
        securityLogger.warn('Suspicious request: Invalid user agent', {
          userAgent,
          ip: request.ip,
          url: request.url
        });
        
        return reply.status(400).send({
          success: false,
          message: 'Invalid request'
        });
      }
    };
  }

  /**
   * API key validation middleware
   */
  static validateApiKey(validApiKeys: string[]) {
    return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
      const apiKey = request.headers['x-api-key'] as string;
      
      if (!apiKey) {
        return reply.status(401).send({
          success: false,
          message: 'API key required'
        });
      }

      if (!validApiKeys.includes(apiKey)) {
        securityLogger.warn('Invalid API key used', {
          apiKey: CryptoUtils.maskSensitiveData(apiKey),
          ip: request.ip,
          url: request.url
        });
        
        return reply.status(401).send({
          success: false,
          message: 'Invalid API key'
        });
      }
    };
  }

  /**
   * CSRF protection middleware
   */
  static csrfProtection() {
    return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
      // Skip CSRF for GET, HEAD, OPTIONS
      if (['GET', 'HEAD', 'OPTIONS'].includes(request.method)) {
        return;
      }

      const csrfToken = request.headers['x-csrf-token'] as string;
      const sessionToken = request.cookies?.csrfToken;

      if (!csrfToken || !sessionToken || csrfToken !== sessionToken) {
        securityLogger.warn('CSRF token validation failed', {
          ip: request.ip,
          url: request.url,
          method: request.method
        });
        
        return reply.status(403).send({
          success: false,
          message: 'CSRF token validation failed'
        });
      }
    };
  }

  /**
   * Suspicious activity detector
   */
  static detectSuspiciousActivity() {
    const suspiciousPatterns = [
      /\b(union|select|insert|delete|drop|create|alter)\b/i, // SQL injection
      /<script|javascript:|vbscript:|onload=|onerror=/i, // XSS
      /\.\.\//g, // Path traversal
      /%00|%2e%2e|%252e/i, // Null byte and encoded path traversal
    ];

    return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
      const url = request.url;
      const userAgent = request.headers['user-agent'] || '';
      const referer = request.headers.referer || '';
      
      // Check URL for suspicious patterns
      for (const pattern of suspiciousPatterns) {
        if (pattern.test(url) || pattern.test(userAgent) || pattern.test(referer)) {
          securityLogger.error('Suspicious activity detected', {
            pattern: pattern.toString(),
            url,
            userAgent,
            referer,
            ip: request.ip
          });
          
          return reply.status(400).send({
            success: false,
            message: 'Invalid request'
          });
        }
      }

      // Check request body if present
      if (request.body && typeof request.body === 'string') {
        for (const pattern of suspiciousPatterns) {
          if (pattern.test(request.body)) {
            securityLogger.error('Suspicious content in request body', {
              pattern: pattern.toString(),
              ip: request.ip,
              url
            });
            
            return reply.status(400).send({
              success: false,
              message: 'Invalid request content'
            });
          }
        }
      }
    };
  }

  /**
   * Device fingerprinting middleware
   */
  static deviceFingerprint() {
    return async (request: FastifyRequest, _reply: FastifyReply): Promise<void> => {
      const fingerprint = {
        userAgent: request.headers['user-agent'],
        acceptLanguage: request.headers['accept-language'],
        acceptEncoding: request.headers['accept-encoding'],
        ip: request.ip,
        timestamp: new Date().toISOString()
      };

      (request as any).deviceFingerprint = CryptoUtils.generateHash(JSON.stringify(fingerprint));
    };
  }

  /**
   * Geo-blocking middleware
   */
  // static geoBlock(blockedCountries: string[]) {
  //   return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
  //     // This would typically integrate with a GeoIP service
  //     const countryCode = request.headers['cf-ipcountry'] as string; 
      
  //     if (countryCode && blockedCountries.includes(countryCode.toUpperCase())) {
  //       securityLogger.warn('Geo-blocked request', {
  //         country: countryCode,
  //         ip: request.ip,
  //         url: request.url
  //       });
        
  //       return reply.status(403).send({
  //         success: false,
  //         message: 'Access not available in your region'
  //       });
  //     }
  //   };
  // }

  /**
   * Honeypot middleware - detect bots
   */
  static honeypot() {
    return async (request: FastifyRequest, _reply: FastifyReply): Promise<void> => {
      const honeypotField = (request.body as any)?.honeypot;
      
      if (honeypotField) {
        securityLogger.warn('Bot detected via honeypot', {
          ip: request.ip,
          userAgent: request.headers['user-agent'],
          url: request.url
        });
        
        // Don't immediately reject, just log and continue
        // This makes it harder for bots to detect the honeypot
      }
    };
  }
}
