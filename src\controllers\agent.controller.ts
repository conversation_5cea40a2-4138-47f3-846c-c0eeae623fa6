import { FastifyRequest, FastifyReply } from 'fastify';
import { AuthenticatedRequest } from '../types';
import { AgentService } from '../services/agent.service';
import { logger } from '../config/logger';

export const registerAgent = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required',
      errorCode: 'AUTH_REQUIRED'
    });
  }

  try {
    const data = request.body as any;

    const agent = await AgentService.registerAgent({
      userId: request.user.id,
      ...data
    });

    return reply.status(201).send({
      success: true,
      message: 'agent registration submitted successfully',
      data: {
        agentRegistration: {
          agentId: agent._id.toString(),
          agentCode: agent.agent_code,
          businessName: agent.business_name,
          businessType: agent.business_type,
          agentStatus: agent.agent_status,
          kycStatus: agent.kyc_status,
          registeredAt: agent.created_at
        },
        businessInfo: {
          businessName: agent.business_name,
          businessType: agent.business_type,
          registrationNumber: agent.business_registration_number,
          taxId: agent.tax_id
        },
        locationDetails: {
          address: agent.location.address,
          city: agent.location.city,
          state: agent.location.state,
          country: agent.location.country,
          coordinates: agent.location.coordinates
        },
        nextSteps: {
          kycVerificationRequired: true,
          approvalPending: true,
          estimatedApprovalTime: "2-3 business days"
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        timestamp: new Date().toISOString(),
        serviceType: "agent_registration"
      }
    });
  } catch (error: any) {
    logger.error('agent registration error:', error);
    
    const errorMessages: Record<string, string> = {
      'user already has an agent account': 'you already have an active agent account',
      'user not found': 'user account not found'
    };

    const message = errorMessages[error.message] || 'agent registration failed';
    
    return reply.status(400).send({
      success: false,
      message,
      errorCode: 'REGISTRATION_FAILED',
      errorDetails: {
        reason: error.message,
        timestamp: new Date().toISOString()
      }
    });
  }
};

export const performCashIn = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'agent authentication required'
    });
  }

  try {
    const data = request.body as any;

    const result = await AgentService.performCashIn({
      agentId: request.user.id, // assuming agent user
      ...data
    });

    return reply.status(200).send({
      success: true,
      message: 'cash in transaction completed successfully',
      data: {
        transactionDetails: {
          transactionId: result.transactionId,
          transactionType: "cash_in",
          amount: data.amount,
          currency: data.currency,
          commission: result.commission,
          status: "completed",
          processedAt: new Date().toISOString()
        },
        customerInfo: {
          customerId: data.customerId,
          newBalance: result.customerBalance
        },
        agentInfo: {
          agentId: request.user.id,
          newBalance: result.agentBalance,
          commissionEarned: result.commission
        },
        transactionSummary: {
          customerReceived: data.amount,
          agentCommission: result.commission,
          agentBalanceChange: -data.amount
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        processingTimeMs: Date.now() - ((request as any).startTime || Date.now()),
        transactionType: "cash_in"
      }
    });
  } catch (error: any) {
    logger.error('cash in transaction error:', error);
    
    return reply.status(400).send({
      success: false,
      message: 'cash in transaction failed',
      error: error.message,
      errorCode: 'CASH_IN_FAILED'
    });
  }
};

export const performCashOut = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'agent authentication required'
    });
  }

  try {
    const data = request.body as any;

    const result = await AgentService.performCashOut({
      agentId: request.user.id,
      ...data
    });

    return reply.status(200).send({
      success: true,
      message: 'cash out transaction completed successfully',
      data: {
        transactionDetails: {
          transactionId: result.transactionId,
          transactionType: "cash_out",
          amount: data.amount,
          currency: data.currency,
          commission: result.commission,
          status: "completed",
          processedAt: new Date().toISOString()
        },
        customerInfo: {
          customerId: data.customerId,
          newBalance: result.customerBalance
        },
        agentInfo: {
          agentId: request.user.id,
          newBalance: result.agentBalance,
          commissionEarned: result.commission
        },
        transactionSummary: {
          customerWithdrew: data.amount,
          agentCommission: result.commission,
          agentBalanceChange: data.amount
        }
      },
      metadata: {
        requestId: (request as any).requestId,
        processingTimeMs: Date.now() - ((request as any).startTime || Date.now()),
        transactionType: "cash_out"
      }
    });
  } catch (error: any) {
    logger.error('cash out transaction error:', error);
    
    return reply.status(400).send({
      success: false,
      message: 'cash out transaction failed',
      error: error.message,
      errorCode: 'CASH_OUT_FAILED'
    });
  }
};

export const getAgentProfile = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.id) {
    return reply.status(401).send({
      success: false,
      message: 'authentication required'
    });
  }

  try {
    const agent = await AgentService.getAgentById(request.user.id);
    
    if (!agent) {
      return reply.status(404).send({
        success: false,
        message: 'agent profile not found'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'agent profile retrieved successfully',
      data: {
        agentProfile: {
          agentId: agent._id.toString(),
          agentCode: agent.agent_code,
          businessName: agent.business_name,
          businessType: agent.business_type,
          agentStatus: agent.agent_status,
          kycStatus: agent.kyc_status,
          registeredAt: agent.created_at
        },
        businessDetails: {
          businessName: agent.business_name,
          businessType: agent.business_type,
          registrationNumber: agent.business_registration_number,
          taxId: agent.tax_id
        },
        locationInfo: {
          address: agent.location.address,
          city: agent.location.city,
          state: agent.location.state,
          country: agent.location.country,
          fullLocation: (agent as any).full_location
        },
        contactDetails: {
          phone: agent.contact_info.phone,
          email: agent.contact_info.email,
          whatsapp: agent.contact_info.whatsapp
        },
        walletInfo: {
          availableBalance: agent.wallet_info.available_balance,
          commissionBalance: agent.wallet_info.commission_balance,
          floatBalance: agent.wallet_info.float_balance
        },
        performanceStats: {
          totalTransactions: agent.statistics.total_transactions,
          totalVolume: agent.statistics.total_volume,
          totalCommissionEarned: agent.statistics.total_commission_earned,
          cashInCount: agent.statistics.cash_in_count,
          cashOutCount: agent.statistics.cash_out_count,
          lastTransactionDate: agent.statistics.last_transaction_date,
          performanceMetrics: (agent as any).performance_metrics
        },
        commissionStructure: {
          cashInRate: `${(agent.commission_structure.cash_in_rate * 100).toFixed(2)}%`,
          cashOutRate: `${(agent.commission_structure.cash_out_rate * 100).toFixed(2)}%`,
          billPaymentRate: `${(agent.commission_structure.bill_payment_rate * 100).toFixed(2)}%`,
          transferRate: `${(agent.commission_structure.transfer_rate * 100).toFixed(2)}%`,
          minimumCommission: agent.commission_structure.minimum_commission
        }
      }
    });
  } catch (error: any) {
    logger.error('get agent profile error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve agent profile'
    });
  }
};

export const searchAgents = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const query = request.query as any;

    const result = await AgentService.searchAgents(query);

    return reply.status(200).send({
      success: true,
      message: 'agents retrieved successfully',
      data: {
        agentsList: result.agents.map(agent => ({
          agentId: agent._id.toString(),
          agentCode: agent.agent_code,
          businessName: agent.business_name,
          businessType: agent.business_type,
          agentStatus: agent.agent_status,
          kycStatus: agent.kyc_status,
          location: {
            city: agent.location.city,
            state: agent.location.state,
            country: agent.location.country
          },
          contactInfo: {
            phone: agent.contact_info.phone,
            email: agent.contact_info.email
          },
          statistics: {
            totalTransactions: agent.statistics.total_transactions,
            totalVolume: agent.statistics.total_volume,
            totalCommissionEarned: agent.statistics.total_commission_earned
          },
          registeredAt: agent.created_at
        })),
        pagination: {
          currentPage: result.page,
          totalPages: result.pages,
          totalItems: result.total,
          itemsPerPage: query.limit || 20,
          hasNext: result.page < result.pages,
          hasPrevious: result.page > 1
        }
      },
      metadata: {
        searchFilters: {
          search: query.search || null,
          status: query.status || null,
          kycStatus: query.kycStatus || null,
          city: query.city || null,
          state: query.state || null,
          businessType: query.businessType || null
        },
        resultCount: result.agents.length
      }
    });
  } catch (error: any) {
    logger.error('search agents error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'failed to search agents'
    });
  }
};

export const getAgentByCode = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const { agentCode } = request.params as any;

    const agent = await AgentService.getAgentByCode(agentCode);
    
    if (!agent) {
      return reply.status(404).send({
        success: false,
        message: 'agent not found',
        errorCode: 'AGENT_NOT_FOUND'
      });
    }

    return reply.status(200).send({
      success: true,
      message: 'agent found',
      data: {
        agentInfo: {
          agentId: agent._id.toString(),
          agentCode: agent.agent_code,
          businessName: agent.business_name,
          businessType: agent.business_type,
          agentStatus: agent.agent_status,
          kycStatus: agent.kyc_status
        },
        locationInfo: {
          address: agent.location.address,
          city: agent.location.city,
          state: agent.location.state,
          country: agent.location.country
        },
        contactInfo: {
          phone: agent.contact_info.phone,
          email: agent.contact_info.email
        },
        serviceCapabilities: {
          cashInAvailable: agent.agent_status === 'active',
          cashOutAvailable: agent.agent_status === 'active',
          billPaymentAvailable: agent.agent_status === 'active'
        }
      }
    });
  } catch (error: any) {
    logger.error('get agent by code error:', error);
    
    return reply.status(500).send({
      success: false,
      message: 'failed to retrieve agent information'
    });
  }
};
