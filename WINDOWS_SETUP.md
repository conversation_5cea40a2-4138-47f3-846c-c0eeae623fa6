# AeTrust Backend - Windows Setup Guide

## 🚀 Quick Start for Windows

### Prerequisites Installed ✅
- ✅ Node.js 18+
- ✅ Memurai (Redis for Windows) on port 6379
- ✅ MongoDB (assumed installed)

## 🔧 Step-by-Step Setup

### 1. **Start Memurai (Redis)**
```cmd
# Option 1: Start Memurai service (if installed as service)
net start Memurai

# Option 2: Start Memurai manually
# Navigate to Memurai installation directory and run:
memurai-server.exe

# Option 3: If using Redis directly
redis-server.exe
```

### 2. **Start MongoDB**
```cmd
# Option 1: Start MongoDB service
net start MongoDB

# Option 2: Start MongoDB manually
mongod --dbpath "C:\data\db"

# Option 3: If using MongoDB Atlas (cloud), just update .env
```

### 3. **Configure Environment**
```cmd
# Copy environment template
copy .env.example .env

# Edit .env file with your settings
notepad .env
```

### 4. **Install Dependencies & Start**
```cmd
# Quick start script
scripts\start-windows.bat

# OR manually:
npm install
npm run build
npm run dev
```

## ⚙️ Environment Configuration (.env)

Create/update your `.env` file with these settings:

```env
# Server Configuration
NODE_ENV=development
PORT=3000
HOST=0.0.0.0
API_VERSION=v1

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/aetrust_dev
# OR for MongoDB Atlas:
# MONGODB_URI=mongodb+srv://username:<EMAIL>/aetrust_dev

# Redis Configuration (Memurai)
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
REDIS_FAMILY=4
REDIS_KEEPALIVE=30000
REDIS_CONNECT_TIMEOUT=10000
REDIS_COMMAND_TIMEOUT=5000

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Encryption
ENCRYPTION_KEY=your-32-character-encryption-key-here

# Logging
LOG_LEVEL=debug
LOG_FILE=logs/app.log

# API Rate Limiting
API_RATE_LIMIT=100
API_RATE_WINDOW=15

# External APIs (for production features)
EXCHANGE_API_URL=https://api.exchangerate-api.com/v4/latest
EXCHANGE_API_KEY=your-api-key-here
BILL_PROVIDERS_API_URL=https://api.billproviders.com/v1
BILL_PROVIDERS_API_KEY=your-api-key-here

# Email/SMS (optional for development)
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
TWILIO_PHONE_NUMBER=+**********
SENDGRID_API_KEY=your_sendgrid_key
SENDGRID_FROM_EMAIL=<EMAIL>

# Security
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
BCRYPT_ROUNDS=12

# Development Settings
ENABLE_SWAGGER=true
MOCK_EXTERNAL_APIS=true
SKIP_EMAIL_VERIFICATION=true
```

## 🐳 Docker Alternative (Recommended)

If you prefer Docker (easier setup):

### 1. **Install Docker Desktop for Windows**
Download from: https://www.docker.com/products/docker-desktop/

### 2. **Start with Docker**
```cmd
# Start all services (MongoDB, Redis, API)
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## 🔍 Verify Setup

### 1. **Check Services**
```cmd
# Check if Memurai is running
redis-cli ping
# Should return: PONG

# Check if MongoDB is running
mongosh --eval "db.adminCommand('ping')"
# Should return: { ok: 1 }
```

### 2. **Test API**
```cmd
# Health check
curl http://localhost:3000/health

# OR open in browser:
# http://localhost:3000/health
```

### 3. **Check Logs**
```cmd
# View application logs
type logs\app.log

# OR real-time logs if using npm run dev
# Logs will appear in console
```

## 🛠️ Development Commands

```cmd
# Start development server
npm run dev

# Build for production
npm run build

# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Lint code
npm run lint

# Format code
npm run format

# Start with Docker
npm run docker:dev

# Stop Docker services
npm run docker:down
```

## 🔧 Troubleshooting

### **Memurai/Redis Issues**
```cmd
# Check if Memurai is running
netstat -an | findstr :6379

# Restart Memurai service
net stop Memurai
net start Memurai

# Test connection
redis-cli ping
```

### **MongoDB Issues**
```cmd
# Check if MongoDB is running
netstat -an | findstr :27017

# Restart MongoDB service
net stop MongoDB
net start MongoDB

# Test connection
mongosh --eval "db.adminCommand('ping')"
```

### **Port Conflicts**
```cmd
# Check what's using port 3000
netstat -ano | findstr :3000

# Kill process if needed (replace PID)
taskkill /PID <process_id> /F
```

### **Node.js Issues**
```cmd
# Check Node.js version
node --version

# Clear npm cache
npm cache clean --force

# Reinstall dependencies
rmdir /s node_modules
del package-lock.json
npm install
```

## 📊 Monitoring URLs

Once running, access these URLs:

- **API Health**: http://localhost:3000/health
- **API Docs**: http://localhost:3000/api-docs (if Swagger enabled)
- **Metrics**: http://localhost:3000/metrics

## 🔄 CI/CD Integration

Your GitHub Actions pipeline will work automatically when you push to:
- `main` branch → Production deployment
- `develop` branch → Staging deployment
- Feature branches → Testing only

## 🎯 Next Steps

1. **Update .env** with your actual configuration
2. **Test all endpoints** using Postman or curl
3. **Set up GitHub secrets** for CI/CD deployment
4. **Configure external APIs** for production features
5. **Set up monitoring** for production deployment

## 🆘 Need Help?

1. **Check logs**: `type logs\app.log`
2. **Verify services**: Run health checks above
3. **Restart services**: Stop and start Memurai/MongoDB
4. **Check environment**: Verify .env configuration

## 🎉 Success!

If you see this message when visiting http://localhost:3000/health:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 123.456
}
```

**Your AeTrust Backend is running successfully! 🚀**

## 📝 Quick Commands Summary

```cmd
# Complete setup and start
scripts\start-windows.bat

# Just start development
npm run dev

# Docker alternative
docker-compose up -d

# Health check
curl http://localhost:3000/health
```
