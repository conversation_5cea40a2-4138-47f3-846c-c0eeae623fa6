@echo off
echo.
echo ========================================
echo  AeTrust Backend - Quick Start
echo ========================================
echo.

REM Check if Memurai service exists and start it
echo [INFO] Checking Memurai service...
sc query Memurai >nul 2>&1
if %errorlevel% equ 0 (
    echo [INFO] Memurai service found, starting...
    net start Memurai >nul 2>&1
    if %errorlevel% equ 0 (
        echo [SUCCESS] Memurai started successfully
    ) else (
        echo [WARNING] Memurai service exists but failed to start
        echo [INFO] You can start it manually: net start Memurai
    )
) else (
    echo [WARNING] Memurai service not found
    echo [INFO] Please install Memurai or start redis-server manually
    echo [INFO] The application will work without Redis (caching disabled)
)

echo.

REM Check if MongoDB is running
echo [INFO] Checking MongoDB...
netstat -an | findstr :27017 >nul 2>&1
if %errorlevel% equ 0 (
    echo [SUCCESS] MongoDB is running on port 27017
) else (
    echo [WARNING] MongoDB not detected on port 27017
    echo [INFO] Please start MongoDB service: net start MongoDB
    echo [INFO] Or start mongod manually
)

echo.

REM Create .env if it doesn't exist
if not exist ".env" (
    echo [INFO] Creating .env file...
    copy ".env.example" ".env" >nul
    echo [SUCCESS] .env file created from .env.example
    echo [WARNING] Please update .env with your configuration
    echo.
)

REM Create necessary directories
echo [INFO] Creating directories...
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads
if not exist "backups" mkdir backups

REM Install dependencies if needed
if not exist "node_modules" (
    echo [INFO] Installing dependencies...
    npm install
)

REM Build the application
echo [INFO] Building application...
npm run build

echo.
echo ========================================
echo  Starting AeTrust Backend...
echo ========================================
echo.
echo [INFO] API will be available at: http://localhost:3000
echo [INFO] Health check: http://localhost:3000/health
echo [INFO] Press Ctrl+C to stop the server
echo.

REM Start the development server
npm run dev
