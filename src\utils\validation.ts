import Jo<PERSON> from 'joi';
import { ValidationError } from '../types';

export class ValidationUtils {
  /**
   * Validate email format
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate phone number (international format)
   */
  static isValidPhone(phone: string): boolean {
    const phoneRegex = /^\+[1-9]\d{1,14}$/;
    return phoneRegex.test(phone);
  }

  /**
   * Validate password strength
   */
  static validatePassword(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate National ID format (Rwanda)
   */
  static isValidNationalId(nationalId: string): boolean {
    // Rwanda National ID: 16 digits
    const rwandaIdRegex = /^\d{16}$/;
    return rwandaIdRegex.test(nationalId);
  }

  /**
   * Validate amount (positive number with max 2 decimal places)
   */
  static isValidAmount(amount: number | string): boolean {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    
    if (isNaN(numAmount) || numAmount <= 0) {
      return false;
    }
    
    // Check for max 2 decimal places
    const decimalPlaces = (numAmount.toString().split('.')[1] || '').length;
    return decimalPlaces <= 2;
  }

  /**
   * Validate currency code
   */
  static isValidCurrency(currency: string): boolean {
    const validCurrencies = ['USD', 'EUR', 'GBP', 'RWF', 'KES', 'UGX', 'TZS'];
    return validCurrencies.includes(currency.toUpperCase());
  }

  /**
   * Sanitize input string
   */
  static sanitizeString(input: string): string {
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/['"]/g, '') // Remove quotes
      .substring(0, 255); // Limit length
  }

  /**
   * Validate and sanitize user input
   */
  static sanitizeUserInput(data: Record<string, any>): Record<string, any> {
    const sanitized: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'string') {
        sanitized[key] = this.sanitizeString(value);
      } else if (typeof value === 'number') {
        sanitized[key] = isNaN(value) ? 0 : value;
      } else if (typeof value === 'boolean') {
        sanitized[key] = Boolean(value);
      } else if (Array.isArray(value)) {
        sanitized[key] = value.map(item => 
          typeof item === 'string' ? this.sanitizeString(item) : item
        );
      } else {
        sanitized[key] = value;
      }
    }
    
    return sanitized;
  }

  /**
   * Common Joi schemas
   */
  static schemas = {
    email: Joi.string().email().required().messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),

    password: Joi.string().min(8).pattern(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])/
    ).required().messages({
      'string.min': 'Password must be at least 8 characters long',
      'string.pattern.base': 'Password must contain uppercase, lowercase, number and special character',
      'any.required': 'Password is required'
    }),

    phone: Joi.string().pattern(/^\+[1-9]\d{1,14}$/).required().messages({
      'string.pattern.base': 'Please provide a valid phone number with country code',
      'any.required': 'Phone number is required'
    }),

    nationalId: Joi.string().pattern(/^\d{16}$/).required().messages({
      'string.pattern.base': 'National ID must be 16 digits',
      'any.required': 'National ID is required'
    }),

    amount: Joi.number().positive().precision(2).required().messages({
      'number.positive': 'Amount must be positive',
      'number.precision': 'Amount can have maximum 2 decimal places',
      'any.required': 'Amount is required'
    }),

    currency: Joi.string().valid('USD', 'EUR', 'GBP', 'RWF', 'KES', 'UGX', 'TZS').required().messages({
      'any.only': 'Invalid currency code',
      'any.required': 'Currency is required'
    }),

    pagination: Joi.object({
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(20),
      sort: Joi.string().optional(),
      order: Joi.string().valid('asc', 'desc').default('desc')
    }),

    objectId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).required().messages({
      'string.pattern.base': 'Invalid ID format',
      'any.required': 'ID is required'
    }),

    uuid: Joi.string().uuid().required().messages({
      'string.uuid': 'Invalid UUID format',
      'any.required': 'UUID is required'
    })
  };

  /**
   * Validate data against Joi schema
   */
  static async validateSchema<T>(
    data: any, 
    schema: Joi.ObjectSchema<T>
  ): Promise<{ isValid: boolean; data?: T; errors?: ValidationError[] }> {
    try {
      const { error, value } = schema.validate(data, { 
        abortEarly: false,
        stripUnknown: true,
        convert: true
      });

      if (error) {
        const errors: ValidationError[] = error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value
        }));

        return { isValid: false, errors };
      }

      return { isValid: true, data: value };
    } catch (error) {
      return {
        isValid: false,
        errors: [{ field: 'general', message: 'Validation error occurred' }]
      };
    }
  }

  /**
   * Check if string contains only alphanumeric characters
   */
  static isAlphanumeric(str: string): boolean {
    return /^[a-zA-Z0-9]+$/.test(str);
  }

  /**
   * Check if string is a valid URL
   */
  static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate file type
   */
  static isValidFileType(mimetype: string, allowedTypes: string[]): boolean {
    return allowedTypes.includes(mimetype);
  }

  /**
   * Validate file size
   */
  static isValidFileSize(size: number, maxSize: number): boolean {
    return size <= maxSize;
  }

  /**
   * Check if date is in the past
   */
  static isDateInPast(date: Date): boolean {
    return date < new Date();
  }

  /**
   * Check if date is in the future
   */
  static isDateInFuture(date: Date): boolean {
    return date > new Date();
  }

  /**
   * Validate age (must be 18 or older)
   */
  static isValidAge(birthDate: Date): boolean {
    const today = new Date();
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      return age - 1 >= 18;
    }
    
    return age >= 18;
  }
}
