import { Savings, ISavings } from '../models/savings.model';
import { WalletService } from './wallet.service';
import { UserService } from './user.service';
import { SavingsType, SavingsStatus, Currency, TransactionType } from '../types';

import { logger } from '../config/logger';
import mongoose from 'mongoose';

export class SavingsService {
  static async createSavingsAccount(data: {
    userId: string;
    accountName: string;
    savingsType: SavingsType;
    currency: Currency;
    targetAmount?: number;
    targetDate?: Date;
    interestRate?: number;
    autoSaveSettings?: {
      enabled: boolean;
      amount: number;
      frequency: 'daily' | 'weekly' | 'monthly';
    };
    goalSettings?: {
      goalName: string;
      goalDescription?: string;
      targetAmount: number;
      targetDate: Date;
    };
  }): Promise<ISavings> {
    try {
      // check if user exists
      const user = await UserService.getUserById(data.userId);
      if (!user) {
        throw new Error('user not found');
      }

      // generate unique account number
      const accountNumber = await this.generateAccountNumber(data.savingsType);

      // set default interest rate based on savings type
      let interestRate = data.interestRate || 0.05; // 5% default
      switch (data.savingsType) {
        case SavingsType.REGULAR:
          interestRate = 0.03; // 3%
          break;
        case SavingsType.FIXED_DEPOSIT:
          interestRate = 0.08; // 8%
          break;
        case SavingsType.TARGET_SAVINGS:
          interestRate = 0.05; // 5%
          break;
        case SavingsType.EMERGENCY_FUND:
          interestRate = 0.04; // 4%
          break;
      }

      // calculate maturity date for fixed deposits
      let maturityDate: Date | undefined;
      if (data.savingsType === SavingsType.FIXED_DEPOSIT && data.targetDate) {
        maturityDate = data.targetDate;
      }

      // create savings account
      const savings = new Savings({
        user_id: data.userId,
        account_number: accountNumber,
        account_name: data.accountName,
        savings_type: data.savingsType,
        currency: data.currency,
        target_amount: data.targetAmount,
        target_date: data.targetDate,
        interest_rate: interestRate,
        maturity_date: maturityDate,
        auto_save_settings: data.autoSaveSettings ? {
          ...data.autoSaveSettings,
          next_deduction_date: this.calculateNextDeductionDate(data.autoSaveSettings.frequency)
        } : undefined,
        goal_settings: data.goalSettings
      });

      await savings.save();

      logger.info('savings account created', {
        savingsId: savings._id,
        accountNumber: savings.account_number,
        userId: data.userId,
        savingsType: data.savingsType
      });

      return savings;
    } catch (error: any) {
      logger.error('error creating savings account:', error);
      throw error;
    }
  }

  static async deposit(
    savingsId: string,
    amount: number,
    description?: string
  ): Promise<{
    success: boolean;
    newBalance: number;
    transactionId: string;
  }> {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();

      const savings = await Savings.findById(savingsId).session(session);
      if (!savings) {
        throw new Error('savings account not found');
      }

      if (savings.status !== SavingsStatus.ACTIVE) {
        throw new Error('savings account is not active');
      }

      // get user's main wallet
      const wallet = await WalletService.getDefaultWallet(savings.user_id.toString(), savings.currency);
      if (!wallet) {
        throw new Error('user wallet not found');
      }

      // check wallet balance
      if (wallet.available_balance < amount) {
        throw new Error('insufficient wallet balance');
      }

      // debit from wallet
      const walletResult = await WalletService.debitWallet({
        walletId: wallet._id.toString(),
        amount,
        description: description || `Deposit to savings account ${savings.account_number}`,
        transactionType: TransactionType.SAVINGS_DEPOSIT,
        metadata: {
          savingsAccountId: savings._id,
          savingsAccountNumber: savings.account_number
        }
      });

      // credit to savings account
      (savings as any).deposit(amount, description || 'Deposit', walletResult.transaction._id);
      await savings.save({ session });

      await session.commitTransaction();

      logger.info('savings deposit completed', {
        savingsId,
        amount,
        newBalance: savings.current_balance,
        transactionId: walletResult.transaction._id
      });

      return {
        success: true,
        newBalance: savings.current_balance,
        transactionId: walletResult.transaction._id.toString()
      };
    } catch (error: any) {
      await session.abortTransaction();
      logger.error('savings deposit failed:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async withdraw(
    savingsId: string,
    amount: number,
    description?: string
  ): Promise<{
    success: boolean;
    newBalance: number;
    transactionId: string;
    penalty?: number;
  }> {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();

      const savings = await Savings.findById(savingsId).session(session);
      if (!savings) {
        throw new Error('savings account not found');
      }

      if (savings.status !== SavingsStatus.ACTIVE) {
        throw new Error('savings account is not active');
      }

      // attempt withdrawal
      const withdrawalResult = (savings as any).withdraw(
        amount, 
        description || 'Withdrawal', 
        new mongoose.Types.ObjectId()
      );

      if (!withdrawalResult.success) {
        throw new Error(withdrawalResult.message);
      }

      // get user's main wallet
      const wallet = await WalletService.getDefaultWallet(savings.user_id.toString(), savings.currency);
      if (!wallet) {
        throw new Error('user wallet not found');
      }

      // credit to wallet (minus penalty if any)
      const creditAmount = withdrawalResult.penalty ? amount - withdrawalResult.penalty : amount;
      const walletResult = await WalletService.creditWallet({
        walletId: wallet._id.toString(),
        amount: creditAmount,
        description: description || `Withdrawal from savings account ${savings.account_number}`,
        transactionType: TransactionType.SAVINGS_WITHDRAWAL,
        metadata: {
          savingsAccountId: savings._id,
          savingsAccountNumber: savings.account_number,
          penalty: withdrawalResult.penalty
        }
      });

      // update transaction history with correct transaction ID
      const lastTransaction = savings.transaction_history[savings.transaction_history.length - 1];
      if (lastTransaction) {
        lastTransaction.transaction_id = walletResult.transaction._id;
      }

      await savings.save({ session });

      await session.commitTransaction();

      logger.info('savings withdrawal completed', {
        savingsId,
        amount,
        penalty: withdrawalResult.penalty,
        newBalance: savings.current_balance,
        transactionId: walletResult.transaction._id
      });

      return {
        success: true,
        newBalance: savings.current_balance,
        transactionId: walletResult.transaction._id.toString(),
        penalty: withdrawalResult.penalty
      };
    } catch (error: any) {
      await session.abortTransaction();
      logger.error('savings withdrawal failed:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async getUserSavingsAccounts(
    userId: string,
    savingsType?: SavingsType
  ): Promise<ISavings[]> {
    try {
      const query: any = { user_id: userId };
      if (savingsType) query.savings_type = savingsType;

      const accounts = await Savings.find(query)
        .sort({ created_at: -1 })
        .lean();

      return accounts as ISavings[];
    } catch (error: any) {
      logger.error('error getting user savings accounts:', error);
      throw error;
    }
  }

  static async getSavingsAccountById(savingsId: string): Promise<ISavings | null> {
    try {
      if (!mongoose.Types.ObjectId.isValid(savingsId)) {
        return null;
      }

      const savings = await Savings.findById(savingsId)
        .populate('user_id', 'first_name last_name email');

      return savings;
    } catch (error: any) {
      logger.error('error getting savings account by id:', error);
      return null;
    }
  }

  static async processAutoSave(): Promise<void> {
    try {
      const autoSaveAccounts = await Savings.find({
        'auto_save_settings.enabled': true,
        'auto_save_settings.next_deduction_date': { $lte: new Date() },
        status: SavingsStatus.ACTIVE
      });

      for (const account of autoSaveAccounts) {
        try {
          const autoSaveAmount = account.auto_save_settings?.amount || 0;
          
          await this.deposit(
            account._id.toString(),
            autoSaveAmount,
            'Auto-save deposit'
          );

          // update next deduction date
          if (account.auto_save_settings) {
            account.auto_save_settings.next_deduction_date = this.calculateNextDeductionDate(
              account.auto_save_settings.frequency
            );
            await account.save();
          }

          logger.info('auto-save processed', {
            savingsId: account._id,
            amount: autoSaveAmount,
            nextDeduction: account.auto_save_settings?.next_deduction_date
          });
        } catch (error: any) {
          logger.error('auto-save failed for account', {
            savingsId: account._id,
            error: error.message
          });
        }
      }
    } catch (error: any) {
      logger.error('error processing auto-save:', error);
    }
  }

  static async calculateAndApplyInterest(): Promise<void> {
    try {
      const activeAccounts = await Savings.find({
        status: SavingsStatus.ACTIVE,
        current_balance: { $gt: 0 }
      });

      for (const account of activeAccounts) {
        try {
          const interest = (account as any).calculateInterest();
          
          if (interest > 0) {
            const transactionId = new mongoose.Types.ObjectId();
            (account as any).applyInterest(transactionId);
            await account.save();

            logger.info('interest applied', {
              savingsId: account._id,
              interest,
              newBalance: account.current_balance
            });
          }
        } catch (error: any) {
          logger.error('interest calculation failed for account', {
            savingsId: account._id,
            error: error.message
          });
        }
      }
    } catch (error: any) {
      logger.error('error calculating interest:', error);
    }
  }

  private static async generateAccountNumber(savingsType: SavingsType): Promise<string> {
    try {
      const typePrefix = {
        [SavingsType.REGULAR]: 'SAV',
        [SavingsType.FIXED_DEPOSIT]: 'FD',
        [SavingsType.TARGET_SAVINGS]: 'TGT',
        [SavingsType.EMERGENCY_FUND]: 'EMG'
      };

      let accountNumber: string;
      let isUnique = false;
      let attempts = 0;

      do {
        const randomNum = Math.floor(Math.random() * 900000) + 100000; // 6 digits
        accountNumber = `${typePrefix[savingsType]}${randomNum}`;
        
        const existing = await Savings.findOne({ account_number: accountNumber });
        isUnique = !existing;
        attempts++;
      } while (!isUnique && attempts < 10);

      if (!isUnique) {
        // fallback to timestamp-based number
        accountNumber = `${typePrefix[savingsType]}${Date.now().toString().slice(-6)}`;
      }

      return accountNumber;
    } catch (error: any) {
      logger.error('error generating account number:', error);
      return `SAV${Date.now().toString().slice(-6)}`;
    }
  }

  private static calculateNextDeductionDate(frequency: 'daily' | 'weekly' | 'monthly'): Date {
    const now = new Date();
    
    switch (frequency) {
      case 'daily':
        now.setDate(now.getDate() + 1);
        break;
      case 'weekly':
        now.setDate(now.getDate() + 7);
        break;
      case 'monthly':
        now.setMonth(now.getMonth() + 1);
        break;
    }
    
    return now;
  }

  static async closeSavingsAccount(savingsId: string): Promise<boolean> {
    try {
      const savings = await Savings.findById(savingsId);
      if (!savings) {
        throw new Error('savings account not found');
      }

      if (savings.current_balance > 0) {
        throw new Error('cannot close account with remaining balance');
      }

      savings.status = SavingsStatus.CLOSED;
      await savings.save();

      logger.info('savings account closed', {
        savingsId,
        accountNumber: savings.account_number
      });

      return true;
    } catch (error: any) {
      logger.error('error closing savings account:', error);
      throw error;
    }
  }
}
