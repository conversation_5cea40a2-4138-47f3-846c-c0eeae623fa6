import { Loan, <PERSON>oan } from '../models/loan.model';
import { Transaction } from '../models/transaction.model';
import { UserService } from './user.service';
import { WalletService } from './wallet.service';
import { LoanType, LoanStatus, RepaymentStatus, Currency, TransactionType } from '../types';
import { logger } from '../config/logger';
import mongoose from 'mongoose';

export class LoanService {
  static async applyForLoan(data: {
    userId: string;
    loanType: LoanType;
    loanAmount: number;
    currency: Currency;
    loanTermMonths: number;
    purpose: string;
    monthlyIncome: number;
    employmentStatus: string;
    employerName?: string;
    employmentDurationMonths?: number;
    otherIncome?: number;
    existingLoans?: number;
    collateralType?: string;
    collateralValue?: number;
  }): Promise<ILoan> {
    try {
      // get user
      const user = await UserService.getUserById(data.userId);
      if (!user) {
        throw new Error('user not found');
      }

      // check if user has active loans
      const activeLoans = await Loan.find({
        user_id: data.userId,
        status: { $in: [LoanStatus.ACTIVE, LoanStatus.DISBURSED] }
      });

      if (activeLoans.length >= 3) {
        throw new Error('maximum active loans limit reached');
      }

      // calculate interest rate based on loan type and user profile
      const interestRate = this.calculateInterestRate(data.loanType, data.monthlyIncome, data.existingLoans || 0);

      // create loan application
      const loan = new Loan({
        user_id: data.userId,
        loan_type: data.loanType,
        loan_amount: data.loanAmount,
        currency: data.currency,
        interest_rate: interestRate,
        loan_term_months: data.loanTermMonths,
        purpose: data.purpose,
        status: LoanStatus.PENDING,
        application_data: {
          monthly_income: data.monthlyIncome,
          employment_status: data.employmentStatus,
          employer_name: data.employerName || '',
          employment_duration_months: data.employmentDurationMonths || 0,
          other_income: data.otherIncome || 0,
          existing_loans: data.existingLoans || 0,
          collateral_type: data.collateralType || '',
          collateral_value: data.collateralValue || 0
        }
      });

      // generate repayment schedule
      (loan as any).generateRepaymentSchedule();

      await loan.save();

      logger.info('loan application submitted', {
        loanId: loan._id,
        userId: data.userId,
        amount: data.loanAmount,
        type: data.loanType
      });

      return loan;
    } catch (error: any) {
      logger.error('error applying for loan:', error);
      throw error;
    }
  }

  static async approveLoan(
    loanId: string,
    approverId: string,
    notes?: string
  ): Promise<boolean> {
    try {
      const loan = await Loan.findById(loanId);
      if (!loan) {
        throw new Error('loan not found');
      }

      if (loan.status !== LoanStatus.PENDING) {
        throw new Error('loan is not in pending status');
      }

      // perform credit assessment
      const creditAssessment = await this.performCreditAssessment(loan);
      if (!creditAssessment.approved) {
        throw new Error(`loan rejected: ${creditAssessment.reason}`);
      }

      loan.status = LoanStatus.APPROVED;
      loan.approval_data = {
        approved_by: new mongoose.Types.ObjectId(approverId),
        approved_at: new Date(),
        approval_notes: notes || '',
        credit_limit: creditAssessment.creditLimit || 0
      };

      await loan.save();

      logger.info('loan approved', {
        loanId,
        approverId,
        amount: loan.loan_amount
      });

      return true;
    } catch (error: any) {
      logger.error('error approving loan:', error);
      throw error;
    }
  }

  static async disburseLoan(
    loanId: string,
    disbursementMethod: 'wallet' | 'bank',
    bankDetails?: any
  ): Promise<boolean> {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();

      const loan = await Loan.findById(loanId).session(session);
      if (!loan) {
        throw new Error('loan not found');
      }

      if (loan.status !== LoanStatus.APPROVED) {
        throw new Error('loan is not approved for disbursement');
      }

      if (disbursementMethod === 'wallet') {
        // disburse to user wallet
        const wallet = await WalletService.getDefaultWallet(loan.user_id.toString(), loan.currency);
        if (!wallet) {
          throw new Error('user wallet not found');
        }

        await WalletService.creditWallet({
          walletId: wallet._id.toString(),
          amount: loan.loan_amount,
          description: `Loan disbursement - ${loan.loan_type}`,
          transactionType: TransactionType.LOAN_DISBURSEMENT,
          metadata: {
            loanId: loan._id,
            loanType: loan.loan_type,
            interestRate: loan.interest_rate,
            termMonths: loan.loan_term_months
          }
        });

        loan.disbursement_data = {
          disbursed_at: new Date(),
          disbursed_amount: loan.loan_amount,
          disbursement_method: 'wallet',
          wallet_id: wallet._id
        };
      } else {
        if (!bankDetails || !bankDetails.accountNumber || !bankDetails.bankName) {
          throw new Error('bank details required for bank disbursement');
        }

        if (!/^\d{10,20}$/.test(bankDetails.accountNumber)) {
          throw new Error('invalid bank account number format');
        }

        const transferReference = `LOAN_${loan._id}_${Date.now()}`;

        // create external transaction record
        await Transaction.create({
          transaction_ref: transferReference,
          user_id: loan.user_id,
          wallet_id: null, // external transaction
          type: TransactionType.LOAN_DISBURSEMENT,
          status: 'completed',
          amount: loan.loan_amount,
          fee: 0,
          currency: loan.currency,
          description: `Loan disbursement - ${loan.loan_type}`,
          external_reference: transferReference,
          external_provider: bankDetails.bankName,
          balance_before: 0,
          balance_after: 0,
          metadata: {
            loan_id: loan._id.toString(),
            bank_account: bankDetails.accountNumber,
            bank_name: bankDetails.bankName,
            account_name: bankDetails.accountName,
            swift_code: bankDetails.swiftCode,
            routing_number: bankDetails.routingNumber
          },
          processing: {
            initiated_at: new Date(),
            completed_at: new Date(),
            retry_count: 0
          }
        });

        loan.disbursement_data = {
          disbursed_at: new Date(),
          disbursed_amount: loan.loan_amount,
          disbursement_method: 'bank',
          bank_details: {
            bank_name: bankDetails.bankName,
            account_number: bankDetails.accountNumber,
            account_name: bankDetails.accountName,
            transfer_reference: transferReference,
            swift_code: bankDetails.swiftCode,
            routing_number: bankDetails.routingNumber
          }
        };
      }

      loan.status = LoanStatus.DISBURSED;
      await loan.save({ session });

      await session.commitTransaction();

      logger.info('loan disbursed', {
        loanId,
        amount: loan.loan_amount,
        method: disbursementMethod
      });

      return true;
    } catch (error: any) {
      await session.abortTransaction();
      logger.error('error disbursing loan:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async makeRepayment(
    loanId: string,
    amount: number,
    paymentMethod: 'wallet' | 'bank' = 'wallet'
  ): Promise<{
    success: boolean;
    message: string;
    remainingBalance: number;
    nextPaymentDate?: Date;
  }> {
    const session = await mongoose.startSession();
    
    try {
      session.startTransaction();

      const loan = await Loan.findById(loanId).session(session);
      if (!loan) {
        throw new Error('loan not found');
      }

      if (![LoanStatus.ACTIVE, LoanStatus.DISBURSED].includes(loan.status)) {
        throw new Error('loan is not active');
      }

      if (paymentMethod === 'wallet') {
        // debit from user wallet
        const wallet = await WalletService.getDefaultWallet(loan.user_id.toString(), loan.currency);
        if (!wallet) {
          throw new Error('user wallet not found');
        }

        await WalletService.debitWallet({
          walletId: wallet._id.toString(),
          amount,
          description: `Loan repayment - ${loan.loan_type}`,
          transactionType: TransactionType.LOAN_REPAYMENT,
          metadata: {
            loanId: loan._id,
            paymentNumber: loan.repayment_schedule.findIndex(p => p.status === RepaymentStatus.PENDING) + 1
          }
        });
      }

      // apply payment to loan
      const paymentResult = (loan as any).makePayment(amount);
      if (!paymentResult.success) {
        throw new Error(paymentResult.message);
      }

      await loan.save({ session });

      await session.commitTransaction();

      logger.info('loan repayment processed', {
        loanId,
        amount,
        remainingBalance: loan.outstanding_amount
      });

      return {
        success: true,
        message: paymentResult.message,
        remainingBalance: loan.outstanding_amount,
        ...(loan.next_payment_date && { nextPaymentDate: loan.next_payment_date })
      };
    } catch (error: any) {
      await session.abortTransaction();
      logger.error('error processing loan repayment:', error);
      throw error;
    } finally {
      session.endSession();
    }
  }

  static async getUserLoans(
    userId: string,
    status?: LoanStatus
  ): Promise<ILoan[]> {
    try {
      const query: any = { user_id: userId };
      if (status) query.status = status;

      const loans = await Loan.find(query)
        .sort({ created_at: -1 })
        .populate('approval_data.approved_by', 'first_name last_name');

      return loans;
    } catch (error: any) {
      logger.error('error getting user loans:', error);
      throw error;
    }
  }

  static async getLoanById(loanId: string): Promise<ILoan | null> {
    try {
      if (!mongoose.Types.ObjectId.isValid(loanId)) {
        return null;
      }

      const loan = await Loan.findById(loanId)
        .populate('user_id', 'first_name last_name email phone')
        .populate('approval_data.approved_by', 'first_name last_name');

      return loan;
    } catch (error: any) {
      logger.error('error getting loan by id:', error);
      return null;
    }
  }

  private static calculateInterestRate(
    loanType: LoanType,
    monthlyIncome: number,
    existingLoans: number
  ): number {
    let baseRate = 0.15; // 15% annual

    // adjust based on loan type
    switch (loanType) {
      case LoanType.PERSONAL:
        baseRate = 0.18;
        break;
      case LoanType.BUSINESS:
        baseRate = 0.12;
        break;
      case LoanType.MICROLOAN:
        baseRate = 0.20;
        break;
      case LoanType.BNPL:
        baseRate = 0.25;
        break;
      case LoanType.SALARY_ADVANCE:
        baseRate = 0.10;
        break;
    }

    // adjust based on income
    if (monthlyIncome > 5000) {
      baseRate -= 0.02; // reduce by 2%
    } else if (monthlyIncome < 1000) {
      baseRate += 0.03; // increase by 3%
    }

    // adjust based on existing loans
    if (existingLoans > 0) {
      baseRate += existingLoans * 0.01; // 1% per existing loan
    }

    return Math.min(Math.max(baseRate, 0.08), 0.30); // cap between 8% and 30%
  }

  private static async performCreditAssessment(loan: ILoan): Promise<{
    approved: boolean;
    reason?: string;
    creditLimit?: number;
  }> {
    try {
      const { application_data } = loan;

      // basic eligibility checks
      if (application_data.monthly_income < 500) {
        return { approved: false, reason: 'insufficient monthly income' };
      }

      if ((application_data.existing_loans || 0) > 2) {
        return { approved: false, reason: 'too many existing loans' };
      }

      // debt-to-income ratio
      const debtToIncomeRatio = (loan.monthly_payment + ((application_data.existing_loans || 0) * 200)) / application_data.monthly_income;
      if (debtToIncomeRatio > 0.4) {
        return { approved: false, reason: 'debt-to-income ratio too high' };
      }

      // calculate credit limit
      const creditLimit = application_data.monthly_income * 6; // 6 months of income

      if (loan.loan_amount > creditLimit) {
        return { approved: false, reason: 'loan amount exceeds credit limit' };
      }

      return {
        approved: true,
        creditLimit
      };
    } catch (error: any) {
      logger.error('error in credit assessment:', error);
      return { approved: false, reason: 'credit assessment failed' };
    }
  }

  static async getOverdueLoans(): Promise<ILoan[]> {
    try {
      const loans = await Loan.find({
        status: { $in: [LoanStatus.ACTIVE, LoanStatus.DISBURSED] },
        days_overdue: { $gt: 0 }
      })
      .sort({ days_overdue: -1 })
      .populate('user_id', 'first_name last_name email phone');

      return loans;
    } catch (error: any) {
      logger.error('error getting overdue loans:', error);
      return [];
    }
  }

  static async calculateLateFees(): Promise<void> {
    try {
      const overdueLoans = await this.getOverdueLoans();

      for (const loan of overdueLoans) {
        if (loan.days_overdue > 7) {
          const lateFeeRate = 0.05; // 5% of monthly payment
          const additionalFee = loan.monthly_payment * lateFeeRate;
          
          loan.late_fees += additionalFee;
          await loan.save();

          logger.info('late fee applied', {
            loanId: loan._id,
            daysOverdue: loan.days_overdue,
            lateFee: additionalFee
          });
        }
      }
    } catch (error: any) {
      logger.error('error calculating late fees:', error);
    }
  }
}
