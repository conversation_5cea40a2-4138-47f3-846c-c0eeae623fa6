import mongoose, { Schema, Document } from 'mongoose';
import { UserRole, KycStatus, AccountStatus } from '../types';

export interface IUser extends Document {
  _id: mongoose.Types.ObjectId;
  email: string;
  password: string;
  phone: string;
  username?: string;
  first_name: string;
  last_name: string;
  profile_picture?: string;
  bio?: string;
  date_of_birth: Date;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    postal_code?: string;
  };
  role: UserRole;
  is_verified: boolean;
  kyc_status: KycStatus;
  account_status: AccountStatus;
  wallet_balance: number;
  agent_info?: {
    commission_rate: number;
    total_transactions: number;
    total_commission_earned: number;
    is_active: boolean;
  };
  merchant_info?: {
    business_name?: string;
    business_type?: string;
    business_registration?: string;
    tax_id?: string;
  };
  security: {
    login_attempts: number;
    locked_until?: Date;
    password_reset_token?: string;
    password_reset_expires?: Date;
    email_verification_token?: string;
    email_verification_expires?: Date;
    two_factor_enabled: boolean;
    last_login?: Date;
    last_login_ip?: string;
  };
  preferences: {
    language: string;
    currency: string;
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
    };
  };
  created_at: Date;
  updated_at: Date;
  deleted_at?: Date;
}

const userSchema = new Schema<IUser>({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    index: true
  },
  password: {
    type: String,
    required: true,
    minlength: 8
  },
  phone: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  username: {
    type: String,
    unique: true,
    sparse: true,
    trim: true,
    lowercase: true
  },
  first_name: {
    type: String,
    required: true,
    trim: true
  },
  last_name: {
    type: String,
    required: true,
    trim: true
  },
  profile_picture: {
    type: String,
    default: null
  },
  bio: {
    type: String,
    maxlength: 500,
    default: ''
  },
  date_of_birth: {
    type: Date,
    required: true
  },
  address: {
    street: { type: String, default: '' },
    city: { type: String, default: '' },
    state: { type: String, default: '' },
    country: { type: String, default: '' },
    postal_code: { type: String, default: '' }
  },
  role: {
    type: String,
    enum: Object.values(UserRole),
    default: UserRole.USER,
    index: true
  },
  is_verified: {
    type: Boolean,
    default: false,
    index: true
  },
  kyc_status: {
    type: String,
    enum: Object.values(KycStatus),
    default: KycStatus.PENDING,
    index: true
  },
  account_status: {
    type: String,
    enum: Object.values(AccountStatus),
    default: AccountStatus.ACTIVE,
    index: true
  },
  wallet_balance: {
    type: Number,
    default: 0,
    min: 0
  },
  agent_info: {
    commission_rate: { type: Number, default: 0.02 },
    total_transactions: { type: Number, default: 0 },
    total_commission_earned: { type: Number, default: 0 },
    is_active: { type: Boolean, default: true }
  },
  merchant_info: {
    business_name: { type: String, default: '' },
    business_type: { type: String, default: '' },
    business_registration: { type: String, default: '' },
    tax_id: { type: String, default: '' }
  },
  security: {
    login_attempts: { type: Number, default: 0 },
    locked_until: { type: Date, default: null },
    password_reset_token: { type: String, default: null },
    password_reset_expires: { type: Date, default: null },
    email_verification_token: { type: String, default: null },
    email_verification_expires: { type: Date, default: null },
    two_factor_enabled: { type: Boolean, default: false },
    last_login: { type: Date, default: null },
    last_login_ip: { type: String, default: null }
  },
  preferences: {
    language: { type: String, default: 'en' },
    currency: { type: String, default: 'USD' },
    notifications: {
      email: { type: Boolean, default: true },
      sms: { type: Boolean, default: true },
      push: { type: Boolean, default: true }
    }
  },
  created_at: {
    type: Date,
    default: Date.now,
    index: true
  },
  updated_at: {
    type: Date,
    default: Date.now
  },
  deleted_at: {
    type: Date,
    default: null,
    index: true
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  toJSON: {
    transform: function(_doc: any, ret: any) {
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      delete ret.password;
      return ret;
    }
  }
});

// indexes for performance
userSchema.index({ email: 1, account_status: 1 });
userSchema.index({ phone: 1, account_status: 1 });
userSchema.index({ role: 1, account_status: 1 });
userSchema.index({ kyc_status: 1, created_at: -1 });
userSchema.index({ 'security.last_login': -1 });
userSchema.index({ created_at: -1 });

// pre-save middleware
userSchema.pre('save', function(next) {
  this.updated_at = new Date();
  next();
});

// virtual for full name
userSchema.virtual('full_name').get(function() {
  return `${this.first_name} ${this.last_name}`;
});

// methods
userSchema.methods.toSafeObject = function() {
  const obj = this.toObject();
  delete obj.password;
  delete obj.security.password_reset_token;
  delete obj.security.email_verification_token;
  return obj;
};

userSchema.methods.isAccountLocked = function() {
  return this.security.locked_until && this.security.locked_until > new Date();
};

userSchema.methods.incrementLoginAttempts = function() {
  if (this.security.login_attempts >= 5) {
    this.security.locked_until = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
  }
  this.security.login_attempts += 1;
  return this.save();
};

userSchema.methods.resetLoginAttempts = function() {
  this.security.login_attempts = 0;
  this.security.locked_until = undefined;
  return this.save();
};

export const User = mongoose.model<IUser>('User', userSchema);
