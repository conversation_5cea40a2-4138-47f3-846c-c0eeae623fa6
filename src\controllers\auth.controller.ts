import { FastifyRequest, FastifyReply } from 'fastify';
import { AuthenticatedRequest, UserRole, KycStatus } from '../types';
import { AuthMiddleware } from '../middleware/auth.middleware';
import { TwoFAService } from '../services/twofa.service';
import { UserService } from '../services/user.service';
import { ValidationService } from '../services/validation.service';
import { logger, securityLogger } from '../config/logger';
import { CryptoUtils } from '../utils/crypto';

export const register = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const validation = await ValidationService.validateData(request.body, ValidationService.registerSchema);

    if (!validation.isValid) {
      return reply.status(400).send({
        success: false,
        message: 'validation failed',
        errors: validation.errors
      });
    }

    const { email, password, firstName, lastName, phone, dateOfBirth } = validation.data;

    const user = await UserService.createUser({
      email,
      password,
      phone,
      firstName,
      lastName,
      dateOfBirth: new Date(dateOfBirth)
    });

    securityLogger.info('user registration successful', {
      userId: user._id,
      email,
      ip: request.ip
    });

    return reply.status(201).send({
      success: true,
      message: 'account created successfully',
      data: {
        newUser: {
          userId: user._id?.toString(),
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          fullName: `${user.first_name} ${user.last_name}`,
          isVerified: user.is_verified,
          accountStatus: "active",
          createdAt: user.created_at
        },
        nextSteps: {
          emailVerificationRequired: !user.is_verified,
          kycRequired: true,
          profileCompletion: "pending"
        }
      }
    });
  } catch (error: any) {
    logger.error('registration error:', error);

    if (error.message.includes('already exists')) {
      return reply.status(409).send({
        success: false,
        message: 'user already exists with this email or phone'
      });
    }

    return reply.status(500).send({
      success: false,
      message: 'registration failed'
    });
  }
};

export const login = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const validation = await ValidationService.validateData(request.body, ValidationService.loginSchema);

    if (!validation.isValid) {
      return reply.status(400).send({
        success: false,
        message: 'validation failed',
        errors: validation.errors
      });
    }

    const { email, password, twoFactorCode } = validation.data;

    const user = await UserService.getUserByEmail(email);

    if (!user) {
      securityLogger.warn('login failed - user not found', {
        email,
        ip: request.ip
      });

      return reply.status(401).send({
        success: false,
        message: 'invalid credentials'
      });
    }

    // check if account is locked
    if ((user as any).isAccountLocked()) {
      return reply.status(423).send({
        success: false,
        message: 'account temporarily locked due to too many failed attempts'
      });
    }

    // verify password
    const isValidPassword = await CryptoUtils.verifyPassword(password, user.password);

    if (!isValidPassword) {
      await (user as any).incrementLoginAttempts();

      securityLogger.warn('login failed - invalid password', {
        userId: user._id,
        email,
        ip: request.ip
      });

      return reply.status(401).send({
        success: false,
        message: 'invalid credentials'
      });
    }

    // reset login attempts on successful password verification
    await (user as any).resetLoginAttempts();

    // check 2FA if enabled
    const has2FA = await TwoFAService.is2FAEnabled(user._id.toString());
    if (has2FA) {
      if (!twoFactorCode) {
        return reply.status(200).send({
          success: true,
          message: '2FA code required',
          requires2FA: true
        });
      }

      const verification = await TwoFAService.verifyToken(user._id.toString(), twoFactorCode);
      if (!verification.isValid) {
        return reply.status(401).send({
          success: false,
          message: 'invalid 2FA code'
        });
      }
    }

    const userPayload = {
      id: user._id.toString(),
      email: user.email,
      role: user.role,
      isVerified: user.is_verified,
      kycStatus: user.kyc_status
    };

    const token = AuthMiddleware.generateToken(userPayload);
    const refreshToken = AuthMiddleware.generateRefreshToken(userPayload);

    // update last login
    user.security.last_login = new Date();
    user.security.last_login_ip = request.ip;
    await user.save();

    // set cookie
    reply.setCookie('token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 15 * 60 * 1000
    });

    securityLogger.info('user login successful', {
      userId: user._id,
      email,
      ip: request.ip
    });

    return reply.status(200).send({
      success: true,
      message: 'login successful',
      data: {
        authSession: {
          accessToken: token,
          refreshToken,
          tokenType: "Bearer",
          expiresIn: "15m"
        },
        userInfo: {
          userId: user._id.toString(),
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          userRole: user.role,
          isVerified: user.is_verified,
          kycStatus: user.kyc_status,
          lastLogin: new Date().toISOString()
        },
        sessionDetails: {
          loginTime: new Date().toISOString(),
          ipAddress: request.ip,
          userAgent: request.headers['user-agent']
        }
      }
    });
  } catch (error: any) {
    logger.error('login error:', error);
    return reply.status(500).send({
      success: false,
      message: 'login failed'
    });
  }
};

export const logout = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  try {
    const token = request.headers.authorization?.replace('Bearer ', '');
    
    if (token) {
      await AuthMiddleware.blacklistToken(token);
    }

    reply.clearCookie('token');

    securityLogger.info('user logout', {
      userId: request.user?.id,
      ip: request.ip
    });

    return reply.status(200).send({
      success: true,
      message: 'logged out successfully'
    });
  } catch (error: any) {
    logger.error('logout error:', error);
    return reply.status(500).send({
      success: false,
      message: 'logout failed'
    });
  }
};

export const refreshToken = async (request: FastifyRequest, reply: FastifyReply): Promise<any> => {
  try {
    const { refreshToken } = request.body as any;

    if (!refreshToken) {
      return reply.status(400).send({
        success: false,
        message: 'refresh token required'
      });
    }

    const decoded = AuthMiddleware.verifyRefreshToken(refreshToken);

    // get user from database
    const dbUser = await UserService.getUserById(decoded.id);
    if (!dbUser) {
      securityLogger.warn('Refresh token failed: User not found', {
        userId: decoded.id,
        email: decoded.email,
        ip: request.ip
      });

      return reply.status(401).send({
        success: false,
        message: 'invalid refresh token'
      });
    }

    // check if user account is active
    if (dbUser.account_status !== 'active') {
      securityLogger.warn('Refresh token failed: Account not active', {
        userId: decoded.id,
        accountStatus: dbUser.account_status,
        ip: request.ip
      });

      return reply.status(401).send({
        success: false,
        message: 'account is not active'
      });
    }

    const user = {
      id: dbUser._id.toString(),
      email: dbUser.email,
      role: dbUser.role,
      isVerified: dbUser.is_verified,
      kycStatus: dbUser.kyc_status
    };

    const newToken = AuthMiddleware.generateToken(user);
    const newRefreshToken = AuthMiddleware.generateRefreshToken(user);

    return reply.status(200).send({
      success: true,
      message: 'token refreshed',
      data: {
        token: newToken,
        refreshToken: newRefreshToken
      }
    });
  } catch (error: any) {
    logger.error('token refresh error:', error);
    return reply.status(401).send({
      success: false,
      message: 'invalid refresh token'
    });
  }
};

export const setup2FA = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  try {
    if (!request.user) {
      return reply.status(401).send({
        success: false,
        message: 'authentication required'
      });
    }

    const setup = await TwoFAService.generateSetup(request.user.id, request.user.email);

    return reply.status(200).send({
      success: true,
      message: '2FA setup generated',
      data: {
        qrCode: setup.qrCodeUrl,
        backupCodes: setup.backupCodes
      }
    });
  } catch (error: any) {
    logger.error('2FA setup error:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to setup 2FA'
    });
  }
};

export const verify2FA = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  try {
    if (!request.user) {
      return reply.status(401).send({
        success: false,
        message: 'authentication required'
      });
    }

    const { code } = request.body as any;

    if (!code) {
      return reply.status(400).send({
        success: false,
        message: 'verification code required'
      });
    }

    const verification = await TwoFAService.verifyToken(request.user.id, code);

    if (!verification.isValid) {
      return reply.status(400).send({
        success: false,
        message: 'invalid code'
      });
    }

    return reply.status(200).send({
      success: true,
      message: '2FA verified successfully',
      data: {
        usedBackupCode: verification.usedBackupCode || false
      }
    });
  } catch (error: any) {
    logger.error('2FA verification error:', error);
    return reply.status(500).send({
      success: false,
      message: 'verification failed'
    });
  }
};

export const disable2FA = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  try {
    if (!request.user) {
      return reply.status(401).send({
        success: false,
        message: 'authentication required'
      });
    }

    await TwoFAService.disable2FA(request.user.id);

    return reply.status(200).send({
      success: true,
      message: '2FA disabled'
    });
  } catch (error: any) {
    logger.error('2FA disable error:', error);
    return reply.status(500).send({
      success: false,
      message: 'failed to disable 2FA'
    });
  }
};
