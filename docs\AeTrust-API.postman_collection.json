{"info": {"name": "AeTrust Backend API", "description": "Complete API collection for AeTrust fintech platform", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3000/api/v1", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "refresh_token", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePass123!\",\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON><PERSON>\",\n  \"phone\": \"+*********0\",\n  \"dateOfBirth\": \"1990-01-01\"\n}"}, "url": {"raw": "{{base_url}}/auth/register", "host": ["{{base_url}}"], "path": ["auth", "register"]}}, "response": []}, {"name": "Login User", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data.authSession) {", "        pm.collectionVariables.set('access_token', response.data.authSession.accessToken);", "        pm.collectionVariables.set('refresh_token', response.data.authSession.refreshToken);", "    }", "}"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"SecurePass123!\"\n}"}, "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}}, "response": []}, {"name": "Logout User", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/auth/logout", "host": ["{{base_url}}"], "path": ["auth", "logout"]}}, "response": []}, {"name": "Setup 2FA", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/auth/2fa/setup", "host": ["{{base_url}}"], "path": ["auth", "2fa", "setup"]}}, "response": []}, {"name": "Verify 2FA", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"code\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/auth/2fa/verify", "host": ["{{base_url}}"], "path": ["auth", "2fa", "verify"]}}, "response": []}]}, {"name": "User Management", "item": [{"name": "Get Current User", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users/me", "host": ["{{base_url}}"], "path": ["users", "me"]}}, "response": []}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"<PERSON>\",\n  \"lastName\": \"<PERSON>\",\n  \"bio\": \"Updated bio\",\n  \"address\": {\n    \"street\": \"123 Main St\",\n    \"city\": \"New York\",\n    \"state\": \"NY\",\n    \"country\": \"US\",\n    \"postalCode\": \"10001\"\n  }\n}"}, "url": {"raw": "{{base_url}}/users/profile", "host": ["{{base_url}}"], "path": ["users", "profile"]}}, "response": []}, {"name": "Change Password", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"currentPassword\": \"SecurePass123!\",\n  \"newPassword\": \"NewSecurePass456!\",\n  \"confirmPassword\": \"NewSecurePass456!\"\n}"}, "url": {"raw": "{{base_url}}/users/password", "host": ["{{base_url}}"], "path": ["users", "password"]}}, "response": []}, {"name": "Get User Stats", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users/stats", "host": ["{{base_url}}"], "path": ["users", "stats"]}}, "response": []}]}, {"name": "Transfers", "item": [{"name": "Send Money (P2P)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipientEmail\": \"<EMAIL>\",\n  \"amount\": 100.00,\n  \"currency\": \"USD\",\n  \"description\": \"Payment for services\",\n  \"pin\": \"1234\"\n}"}, "url": {"raw": "{{base_url}}/transfers/send", "host": ["{{base_url}}"], "path": ["transfers", "send"]}}, "response": []}, {"name": "Send Remittance", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"recipientCountry\": \"RW\",\n  \"recipientName\": \"John Doe\",\n  \"recipientPhone\": \"+250*********\",\n  \"amount\": 500,\n  \"sourceCurrency\": \"USD\",\n  \"targetCurrency\": \"RWF\",\n  \"deliveryMethod\": \"mobile_money\",\n  \"description\": \"Family support\"\n}"}, "url": {"raw": "{{base_url}}/transfers/remittance", "host": ["{{base_url}}"], "path": ["transfers", "remittance"]}}, "response": []}, {"name": "Pay Bill", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"billType\": \"electricity\",\n  \"provider\": \"EUCL\",\n  \"accountNumber\": \"*********\",\n  \"amount\": 50.00,\n  \"currency\": \"RWF\"\n}"}, "url": {"raw": "{{base_url}}/transfers/bill-payment", "host": ["{{base_url}}"], "path": ["transfers", "bill-payment"]}}, "response": []}, {"name": "Get Transfer History", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/transfers/history?page=1&limit=20", "host": ["{{base_url}}"], "path": ["transfers", "history"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}}, "response": []}, {"name": "Get Exchange Rates", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/transfers/exchange-rates", "host": ["{{base_url}}"], "path": ["transfers", "exchange-rates"]}}, "response": []}, {"name": "Get Bill Providers", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/transfers/bill-providers?country=RW&bill_type=electricity", "host": ["{{base_url}}"], "path": ["transfers", "bill-providers"], "query": [{"key": "country", "value": "RW"}, {"key": "bill_type", "value": "electricity"}]}}, "response": []}]}, {"name": "Health Checks", "item": [{"name": "Basic Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}, "response": []}, {"name": "Detailed Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health/detailed", "host": ["{{base_url}}"], "path": ["health", "detailed"]}}, "response": []}, {"name": "Transfer Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/transfers/health", "host": ["{{base_url}}"], "path": ["transfers", "health"]}}, "response": []}, {"name": "Auth Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/auth/health", "host": ["{{base_url}}"], "path": ["auth", "health"]}}, "response": []}, {"name": "User Service Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/users/health", "host": ["{{base_url}}"], "path": ["users", "health"]}}, "response": []}]}]}